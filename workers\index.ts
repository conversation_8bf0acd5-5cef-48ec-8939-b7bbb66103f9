// workers/index.ts
import { redis } from "./redis.js";
import { JobType, addJob } from "./utils/redis-jobs.js";
import { createLogger } from "./utils/logger.js";

// Import the database connection check
import { ensureDatabaseConnection } from "./utils/prisma.js";

// Import database health reporter
import { DatabaseHealthReporter } from "./utils/dbHealthReporter.js";

// Create a logger for the main worker process
const logger = createLogger("worker-main");

// Initialize database health reporter (reports every hour)
const healthReporter = new DatabaseHealthReporter("auto-apply-worker");

// Import health check server with monitoring system
// This initializes the parent monitoring system for all workers
import "./health.js";

// Import API server
import "./api/index.js";

// Note: Email API server is now started separately from workers/api/index.ts

// Add global error handler for Redis and database errors
process.on("uncaughtException", (error) => {
  const errorStr = error.toString();

  // Handle Redis connection errors
  if (errorStr.includes("Redis") && errorStr.includes("ECONNREFUSED")) {
    console.log(
      "Uncaught Redis connection error (safely ignored):",
      error.message
    );
  }
  // Handle database connection errors
  else if (
    (errorStr.includes("PrismaClient") || errorStr.includes("PostgreSQL")) &&
    (errorStr.includes("connection") || errorStr.includes("ConnectionReset"))
  ) {
    console.log(
      "Uncaught database connection error (will retry):",
      error.message
    );

    // Attempt to reconnect to the database
    import("./utils/prisma.js").then(({ ensureDatabaseConnection }) => {
      ensureDatabaseConnection()
        .then((connected) => {
          if (connected) {
            console.log("Database reconnection successful after error");
          } else {
            console.error("Database reconnection failed after error");
          }
        })
        .catch((reconnectError) => {
          console.error(
            "Error during database reconnection attempt:",
            reconnectError
          );
        });
    });
  } else {
    console.error("Uncaught exception:", error);
  }
});

// Check database connection on startup with enhanced retry logic
ensureDatabaseConnection()
  .then((connected) => {
    if (connected) {
      logger.info(`Database connection successful`);
    } else {
      logger.error(`Database connection failed on startup`);
      logger.error(
        `Workers will continue to run but database operations may fail`
      );
      logger.error(
        `Check your DATABASE_URL environment variable and database server status`
      );

      // Schedule periodic reconnection attempts
      const reconnectInterval = setInterval(async () => {
        try {
          const reconnected = await ensureDatabaseConnection();
          if (reconnected) {
            logger.info(`Database reconnection successful`);
            clearInterval(reconnectInterval);
          }
        } catch (reconnectError) {
          logger.error(`Database reconnection attempt failed:`, reconnectError);
        }
      }, 60000); // Try to reconnect every minute
    }
  })
  .catch((error) => {
    logger.error(`Error checking database connection:`, error);
  });

// Import worker modules
import "./search/index.js";
import "./resume/index.js";
import "./automation/index.js";
import "./email/index.js";
import "./ats/index.js";
import { startJobAlertsWorker } from "./email/job-alerts.js";

// Start the job alerts worker
startJobAlertsWorker().catch((error) => {
  console.error("Failed to start job alerts worker:", error);
});

// Function to add a resume optimization job
export async function addResumeOptimizationJob(
  resumeId: string,
  fileUrl: string
) {
  try {
    const jobId = await addJob(JobType.RESUME_OPTIMIZATION, {
      resumeId,
      fileUrl,
    });
    console.log(
      `Added resume optimization job ${jobId} for resume ${resumeId}`
    );
    return jobId;
  } catch (err) {
    console.error("Failed to add resume optimization job:", err);
    throw err;
  }
}

// Function to add a search job
export async function addSearchJob(query: string, filters: any = {}) {
  try {
    const jobId = await addJob(JobType.SEARCH, { query, filters });
    console.log(`Added search job ${jobId} for query "${query}"`);
    return jobId;
  } catch (err) {
    console.error("Failed to add search job:", err);
    throw err;
  }
}

// Function to add an automation job search
export async function addAutomationJobSearch(
  runId: string,
  profileId: string,
  userId: string,
  keywords: string = "",
  location: string = "",
  resumeId: string | null = null
) {
  try {
    // Publish to the automation channel
    await redis.publish(
      "automation:job-search",
      JSON.stringify({
        runId,
        profileId,
        userId,
        keywords,
        location,
        resumeId,
      })
    );
    console.log(`Published automation job search for run ${runId}`);
    return runId;
  } catch (err) {
    console.error("Failed to add automation job search:", err);
    throw err;
  }
}

// Function to add a resume parsing job
export async function addResumeParsingJob(resumeId: string, filePath: string) {
  try {
    const jobId = await addJob(JobType.RESUME_PARSING, {
      resumeId,
      filePath,
      timestamp: new Date().toISOString(),
    });
    console.log(`Added resume parsing job ${jobId} for resume ${resumeId}`);
    return jobId;
  } catch (err) {
    console.error("Failed to add resume parsing job:", err);
    throw err;
  }
}

// Re-export ATS analysis job function from workers/ats/index.ts
export { addAtsAnalysisJob } from "./ats/index.js";

// Function to add a job-specific analysis job
export async function addJobSpecificAnalysisJob(
  resumeId: string,
  jobId: string,
  userId: string,
  profileId: string
) {
  try {
    const jobAnalysisId = await addJob(JobType.JOB_SPECIFIC_ANALYSIS, {
      resumeId,
      jobId,
      userId,
      profileId,
      timestamp: new Date().toISOString(),
    });
    console.log(
      `Added job-specific analysis job ${jobAnalysisId} for resume ${resumeId} and job ${jobId}`
    );
    return jobAnalysisId;
  } catch (err) {
    console.error("Failed to add job-specific analysis job:", err);
    throw err;
  }
}

// Ensure Redis is connected
if (redis.status !== "ready") {
  logger.info("Waiting for Redis connection...");
  redis.on("ready", () => {
    logger.info("Redis connection established");
  });
}

// Log startup message
logger.info(
  `Workers initialized and ready to process jobs (${
    process.env.NODE_ENV ?? "development"
  } mode)`
);

// Start database health reporting (every hour)
healthReporter.startReporting(async () => {
  try {
    // Get system metrics
    const memoryMetrics = DatabaseHealthReporter.getMemoryMetrics();
    const uptime = DatabaseHealthReporter.getUptime();

    // Get Redis status
    const redisConnected = redis.status === "ready";

    // Get database status
    let dbConnected = false;
    try {
      dbConnected = await ensureDatabaseConnection();
    } catch (error) {
      logger.debug("Database connection check failed:", error);
    }

    // Calculate error rate (simplified)
    const errorRate = 0; // TODO: Implement actual error tracking

    // Determine health status
    const metrics = {
      ...memoryMetrics,
      uptime,
      redisConnected,
      dbConnected,
      errorRate,
      activeWorkers: 0, // TODO: Get actual worker count
      queueSize: 0, // TODO: Get actual queue size
    };

    const status = DatabaseHealthReporter.determineHealthStatus(metrics);

    return { status, metrics };
  } catch (error) {
    logger.error("Error getting health status:", error);
    return {
      status: "outage" as const,
      metrics: { error: "Health check failed" },
    };
  }
});

// Handle process exit signals
process.on("SIGINT", async () => {
  logger.info("Received SIGINT signal, shutting down worker service...");
  await cleanup();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  logger.info(
    `Received SIGTERM signal at ${new Date().toISOString()}, PID: ${
      process.pid
    }, Uptime: ${process.uptime()}s`
  );
  await cleanup();
  logger.info(
    `Cleanup completed at ${new Date().toISOString()}, but keeping service alive to allow workers to finish.`
  );
});

// Cleanup function to close connections
async function cleanup() {
  logger.info("Cleaning up resources...");
  try {
    // Report shutdown and stop health reporting
    await healthReporter.reportShutdown();
    healthReporter.stopReporting();

    // Note: Email API server is now started separately from workers/api/index.ts
    logger.info("Email API server cleanup skipped (handled separately)");

    // Close Redis connection for the main process
    // Note: Worker processes have their own cleanup functions
    if (redis?.disconnect) {
      try {
        redis.disconnect();
        logger.info("Redis connection closed successfully");
      } catch (error) {
        logger.error("Error disconnecting Redis:", error);
      }
    }

    // Disconnect Prisma client
    try {
      const { disconnectPrisma } = await import("./utils/prisma.js");
      await disconnectPrisma();
      logger.info("Database connection closed successfully");
    } catch (dbError) {
      logger.error("Error disconnecting from database:", dbError);
    }

    logger.info("Main process cleanup completed successfully");
  } catch (error) {
    logger.error("Error during main process cleanup:", error);
  }
}
