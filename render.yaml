services:
  # Node.js-based web service (no Docker)
  - type: web
    name: auto-apply-web
    runtime: node
    rootDir: web
    plan: starter
    buildCommand: |
      # Install dependencies for web app
      npm install
      # Copy marketing directory to node_modules for build process
      mkdir -p node_modules/marketing
      cp -r ../marketing/* node_modules/marketing/
      # Build web app (which includes building Sanity Studio)
      npm run build
    startCommand: node build/index.js
    envVars:
      - fromGroup: auto-apply-web-env
      - key: PORT
        value: 3000
      - key: HOST
        value: 0.0.0.0
      - key: WORKER_API_URL
        value: https://auto-apply-worker.onrender.com
      - key: AI_SERVICE_URL
        value: https://auto-apply-ai.onrender.com
    healthCheckPath: /health

  - type: worker
    name: auto-apply-worker
    runtime: docker
    rootDir: workers
    plan: starter
    buildCommand: npm install && npm run generate && npm run build
    envVars:
      - fromGroup: auto-apply-worker-env
      - key: PORT
        value: 10000
      - key: NODE_ENV
        value: production
      - key: OLLAMA_URL
        value: https://ollama.ai/api
      - key: LLM_MODEL
        value: mistral

  - type: worker
    name: auto-apply-scheduler
    runtime: docker
    rootDir: cron
    plan: starter
    buildCommand: npm install && npx prisma generate
    envVars:
      - fromGroup: auto-apply-scheduler-env
      - key: PORT
        value: 10000
      - key: NODE_ENV
        value: production
      - key: TZ
        value: America/New_York

  # Dedicated scraper service for job scraping
  - type: worker
    name: auto-apply-scraper
    runtime: docker
    rootDir: scraper
    plan: standard
    envVars:
      - fromGroup: auto-apply-scraper-env
      - key: NODE_ENV
        value: production
      - key: TZ
        value: America/New_York

  # AI service for resume analysis and optimization with embedded Ollama
  - type: worker
    name: auto-apply-ai
    runtime: docker
    rootDir: ai
    plan: starter
    envVars:
      - fromGroup: auto-apply-ai-env
      - key: PORT
        value: 3100
      - key: HOST
        value: 0.0.0.0
      - key: NODE_ENV
        value: production

envVarGroups:
  # Web service environment variables
  - name: auto-apply-web-env
    envVars:
      - key: GOOGLE_ID
        value: 969352023423-a3apbqh7hrp9fi57d5es6rns722iigvn.apps.googleusercontent.com
      - key: STRIPE_SECRET_KEY_TEST
        value: sk_test_51R8SnVPvxCOa4C05WwTv3Ffo3avvqjr0chEiRMGesmIPpDCmuPiGl3LZ4YqFLrpZp5W8neoavj3fhpEKxRXlUoUo00KOJ6e5kH
      - key: STRIPE_SECRET_KEY_LIVE
        value: pk_live_51R8SnHL0zwkUpKXmx71gq4MzhNjxyIM56jJHQFisYQaG22f9cGu8Rcg7eI2z8pUe7ef1n3WRsC2zTo7TjwsaFQ9X00BuQFNCqO
      - key: STRIPE_WEBHOOK_SECRET
        value: we_1R8TEgPvxCOa4C05d7F9qS79
      - key: JWT_SECRET
        value: d8541061a0d656f1a2340d8d071d5e44d494282f6c5e6b1819f3bfeef43cb03d
      - key: REDIS_URL
        value: redis://red-cvmu1me3jp1c738ve7ig:6379
      - key: RESEND_FROM
        value: <EMAIL>
      - key: PUBLIC_BASE_URL
        value: https://auto-apply-web.onrender.com
      - key: RESEND_API_KEY
        value: re_SdWitKhJ_PXXbWGVbTHm9EfhfWN2g3Wu3
      - key: RESEND_DOMAIN
        value: hirli.co
      - key: DEVELOPER_AUDIENCE
        value: 76c9f8c3-1524-48e3-b9f6-4b5a3a522e82
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: DATABASE_URL
        value: **************************************************************************************************************************
      - key: NODE_ENV
        value: production
      - key: PRISMA_CLIENT_ENGINE_TYPE
        value: binary
      - key: GOOGLE_ID
        value: 969352023423-a3apbqh7hrp9fi57d5es6rns722iigvn.apps.googleusercontent.com
      - key: GOOGLE_SECRET
        value: 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
      - key: LINKEDIN_ID
        value: 77wtjskfw1u9wq
      - key: LINKEDIN_SECRET
        value: WPL_AP1.Bj5eKALOzZQf5biK.qQ2eoQ==
      - key: AUTH_SECRET
        value: H+ZhPVanMs/Dm/18KbxbhZjtAiENPuWQuAo9ut3VlH+sjJc6+57VZOiqPWH0er2u
      - key: AUTH_TRUST_HOST
        value: true
      - key: REDIS_HOST
        value: localhost
      - key: REDIS_PORT
        value: 6379
      - key: EMAIL_API_URL
        value: https://auto-apply-worker.onrender.com/api/email
      - key: SANITY_PROJECT_ID
        value: fqw18aoo
      - key: SANITY_DATASET
        value: production
      - key: SANITY_API_VERSION
        value: 2023-05-03
      - key: VITE_SANITY_PROJECT_ID
        value: fqw18aoo

  # Worker service environment variables
  - name: auto-apply-worker-env
    envVars:
      - key: DATABASE_URL
        value: **************************************************************************************************************************
      - key: REDIS_URL
        value: redis://red-cvmu1me3jp1c738ve7ig:6379
      - key: NODE_ENV
        value: production
      - key: PRISMA_CLIENT_ENGINE_TYPE
        value: binary
      - key: RESEND_API_KEY
        value: re_SdWitKhJ_PXXbWGVbTHm9EfhfWN2g3Wu3
      - key: RESEND_DOMAIN
        value: hirli.co
      - key: DEVELOPER_AUDIENCE
        value: 76c9f8c3-1524-48e3-b9f6-4b5a3a522e82
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: WEB_API_URL
        value: https://auto-apply-web.onrender.com

  # Scheduler service environment variables
  - name: auto-apply-scheduler-env
    envVars:
      - key: DATABASE_URL
        value: **************************************************************************************************************************
      - key: SMARTPROXY_HOST
        value: isp.smartproxy.com
      - key: SMARTPROXY_USERNAME
        value: sptuclupng
      - key: SMARTPROXY_PASSWORD
        value: AJEy0p7zyEzcx01+go
      - key: SMARTPROXY_COUNT
        value: "10"
      - key: RESEND_API_KEY
        value: re_SdWitKhJ_PXXbWGVbTHm9EfhfWN2g3Wu3
      - key: RESEND_DOMAIN
        value: hirli.co
      - key: DEVELOPER_AUDIENCE
        value: 76c9f8c3-1524-48e3-b9f6-4b5a3a522e82
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: NODE_ENV
        value: production
      - key: WEB_API_URL
        value: https://auto-apply-web.onrender.com
      # Redis configuration
      - key: REDIS_URL
        value: redis://red-cvmu1me3jp1c738ve7ig:6379
      - key: REDIS_URL_INTERNAL
        value: redis://red-cvmu1me3jp1c738ve7ig:6379
      - key: REDIS_URL_EXTERNAL
        value: redis://red-cvmu1me3jp1c738ve7ig:6379
      - key: REDIS_HOST
        value: red-cvmu1me3jp1c738ve7ig
      - key: REDIS_PORT
        value: 6379

  # Scraper service environment variables
  - name: auto-apply-scraper-env
    envVars:
      - key: DATABASE_URL
        value: **************************************************************************************************************************
      - key: SMARTPROXY_HOST
        value: isp.smartproxy.com
      - key: SMARTPROXY_USERNAME
        value: sptuclupng
      - key: SMARTPROXY_PASSWORD
        value: AJEy0p7zyEzcx01+go
      - key: SMARTPROXY_COUNT
        value: "10"
      - key: NODE_ENV
        value: production
      - key: REDIS_URL
        value: redis://red-cvmu1me3jp1c738ve7ig:6379
      - key: REDIS_HOST
        value: red-cvmu1me3jp1c738ve7ig
      - key: REDIS_PORT
        value: 6379
      - key: SCRAPER_MAX_WORKERS
        value: "2"
      - key: SCRAPER_BATCH_SIZE
        value: "3"
      - key: SCRAPER_CONCURRENCY
        value: "1"
      - key: JOB_DETAILS_BATCH_SIZE
        value: "2"
      - key: JOB_DETAILS_MAX_CONCURRENT
        value: "1"
      - key: JOB_DETAILS_MAX_JOBS
        value: "100"

  # AI service environment variables
  - name: auto-apply-ai-env
    envVars:
      - key: DATABASE_URL
        value: **************************************************************************************************************************
      - key: REDIS_URL
        value: redis://red-cvmu1me3jp1c738ve7ig:6379
      - key: NODE_ENV
        value: production
      - key: LLM_PROVIDER
        value: ollama
      - key: OLLAMA_URL
        value: http://localhost:11434
      - key: OLLAMA_HOST
        value: 0.0.0.0
      - key: LLM_MODEL
        value: mistral
      - key: LLM_TIMEOUT
        value: 30000
      - key: HEALTH_CHECK_INTERVAL
        value: 600000
      - key: METRICS_INTERVAL
        value: 60000
      - key: MEMORY_THRESHOLD
        value: 85
      - key: CPU_THRESHOLD
        value: 85
      - key: RESET_THRESHOLD
        value: 70
      - key: CIRCUIT_CHECK_INTERVAL
        value: 10000
      - key: LOG_LEVEL
        value: info
