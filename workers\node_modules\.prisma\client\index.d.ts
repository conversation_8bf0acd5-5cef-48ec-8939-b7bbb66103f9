
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model WorkerProcess
 * 
 */
export type WorkerProcess = $Result.DefaultSelection<Prisma.$WorkerProcessPayload>
/**
 * Model SearchJob
 * 
 */
export type SearchJob = $Result.DefaultSelection<Prisma.$SearchJobPayload>
/**
 * Model AtsAnalysis
 * 
 */
export type AtsAnalysis = $Result.DefaultSelection<Prisma.$AtsAnalysisPayload>
/**
 * Model ParsedResume
 * 
 */
export type ParsedResume = $Result.DefaultSelection<Prisma.$ParsedResumePayload>
/**
 * Model ServiceStatus
 * 
 */
export type ServiceStatus = $Result.DefaultSelection<Prisma.$ServiceStatusPayload>
/**
 * Model ServiceStatusHistory
 * 
 */
export type ServiceStatusHistory = $Result.DefaultSelection<Prisma.$ServiceStatusHistoryPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more WorkerProcesses
 * const workerProcesses = await prisma.workerProcess.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more WorkerProcesses
   * const workerProcesses = await prisma.workerProcess.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.workerProcess`: Exposes CRUD operations for the **WorkerProcess** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more WorkerProcesses
    * const workerProcesses = await prisma.workerProcess.findMany()
    * ```
    */
  get workerProcess(): Prisma.WorkerProcessDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.searchJob`: Exposes CRUD operations for the **SearchJob** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more SearchJobs
    * const searchJobs = await prisma.searchJob.findMany()
    * ```
    */
  get searchJob(): Prisma.SearchJobDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.atsAnalysis`: Exposes CRUD operations for the **AtsAnalysis** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more AtsAnalyses
    * const atsAnalyses = await prisma.atsAnalysis.findMany()
    * ```
    */
  get atsAnalysis(): Prisma.AtsAnalysisDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.parsedResume`: Exposes CRUD operations for the **ParsedResume** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ParsedResumes
    * const parsedResumes = await prisma.parsedResume.findMany()
    * ```
    */
  get parsedResume(): Prisma.ParsedResumeDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.serviceStatus`: Exposes CRUD operations for the **ServiceStatus** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ServiceStatuses
    * const serviceStatuses = await prisma.serviceStatus.findMany()
    * ```
    */
  get serviceStatus(): Prisma.ServiceStatusDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.serviceStatusHistory`: Exposes CRUD operations for the **ServiceStatusHistory** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ServiceStatusHistories
    * const serviceStatusHistories = await prisma.serviceStatusHistory.findMany()
    * ```
    */
  get serviceStatusHistory(): Prisma.ServiceStatusHistoryDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.8.2
   * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    WorkerProcess: 'WorkerProcess',
    SearchJob: 'SearchJob',
    AtsAnalysis: 'AtsAnalysis',
    ParsedResume: 'ParsedResume',
    ServiceStatus: 'ServiceStatus',
    ServiceStatusHistory: 'ServiceStatusHistory'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "workerProcess" | "searchJob" | "atsAnalysis" | "parsedResume" | "serviceStatus" | "serviceStatusHistory"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      WorkerProcess: {
        payload: Prisma.$WorkerProcessPayload<ExtArgs>
        fields: Prisma.WorkerProcessFieldRefs
        operations: {
          findUnique: {
            args: Prisma.WorkerProcessFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.WorkerProcessFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>
          }
          findFirst: {
            args: Prisma.WorkerProcessFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.WorkerProcessFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>
          }
          findMany: {
            args: Prisma.WorkerProcessFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>[]
          }
          create: {
            args: Prisma.WorkerProcessCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>
          }
          createMany: {
            args: Prisma.WorkerProcessCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.WorkerProcessCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>[]
          }
          delete: {
            args: Prisma.WorkerProcessDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>
          }
          update: {
            args: Prisma.WorkerProcessUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>
          }
          deleteMany: {
            args: Prisma.WorkerProcessDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.WorkerProcessUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.WorkerProcessUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>[]
          }
          upsert: {
            args: Prisma.WorkerProcessUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$WorkerProcessPayload>
          }
          aggregate: {
            args: Prisma.WorkerProcessAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateWorkerProcess>
          }
          groupBy: {
            args: Prisma.WorkerProcessGroupByArgs<ExtArgs>
            result: $Utils.Optional<WorkerProcessGroupByOutputType>[]
          }
          count: {
            args: Prisma.WorkerProcessCountArgs<ExtArgs>
            result: $Utils.Optional<WorkerProcessCountAggregateOutputType> | number
          }
        }
      }
      SearchJob: {
        payload: Prisma.$SearchJobPayload<ExtArgs>
        fields: Prisma.SearchJobFieldRefs
        operations: {
          findUnique: {
            args: Prisma.SearchJobFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.SearchJobFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>
          }
          findFirst: {
            args: Prisma.SearchJobFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.SearchJobFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>
          }
          findMany: {
            args: Prisma.SearchJobFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>[]
          }
          create: {
            args: Prisma.SearchJobCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>
          }
          createMany: {
            args: Prisma.SearchJobCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.SearchJobCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>[]
          }
          delete: {
            args: Prisma.SearchJobDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>
          }
          update: {
            args: Prisma.SearchJobUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>
          }
          deleteMany: {
            args: Prisma.SearchJobDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.SearchJobUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.SearchJobUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>[]
          }
          upsert: {
            args: Prisma.SearchJobUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchJobPayload>
          }
          aggregate: {
            args: Prisma.SearchJobAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateSearchJob>
          }
          groupBy: {
            args: Prisma.SearchJobGroupByArgs<ExtArgs>
            result: $Utils.Optional<SearchJobGroupByOutputType>[]
          }
          count: {
            args: Prisma.SearchJobCountArgs<ExtArgs>
            result: $Utils.Optional<SearchJobCountAggregateOutputType> | number
          }
        }
      }
      AtsAnalysis: {
        payload: Prisma.$AtsAnalysisPayload<ExtArgs>
        fields: Prisma.AtsAnalysisFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AtsAnalysisFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AtsAnalysisFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>
          }
          findFirst: {
            args: Prisma.AtsAnalysisFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AtsAnalysisFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>
          }
          findMany: {
            args: Prisma.AtsAnalysisFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>[]
          }
          create: {
            args: Prisma.AtsAnalysisCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>
          }
          createMany: {
            args: Prisma.AtsAnalysisCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AtsAnalysisCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>[]
          }
          delete: {
            args: Prisma.AtsAnalysisDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>
          }
          update: {
            args: Prisma.AtsAnalysisUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>
          }
          deleteMany: {
            args: Prisma.AtsAnalysisDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AtsAnalysisUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AtsAnalysisUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>[]
          }
          upsert: {
            args: Prisma.AtsAnalysisUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AtsAnalysisPayload>
          }
          aggregate: {
            args: Prisma.AtsAnalysisAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAtsAnalysis>
          }
          groupBy: {
            args: Prisma.AtsAnalysisGroupByArgs<ExtArgs>
            result: $Utils.Optional<AtsAnalysisGroupByOutputType>[]
          }
          count: {
            args: Prisma.AtsAnalysisCountArgs<ExtArgs>
            result: $Utils.Optional<AtsAnalysisCountAggregateOutputType> | number
          }
        }
      }
      ParsedResume: {
        payload: Prisma.$ParsedResumePayload<ExtArgs>
        fields: Prisma.ParsedResumeFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ParsedResumeFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ParsedResumeFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>
          }
          findFirst: {
            args: Prisma.ParsedResumeFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ParsedResumeFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>
          }
          findMany: {
            args: Prisma.ParsedResumeFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>[]
          }
          create: {
            args: Prisma.ParsedResumeCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>
          }
          createMany: {
            args: Prisma.ParsedResumeCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ParsedResumeCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>[]
          }
          delete: {
            args: Prisma.ParsedResumeDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>
          }
          update: {
            args: Prisma.ParsedResumeUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>
          }
          deleteMany: {
            args: Prisma.ParsedResumeDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ParsedResumeUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ParsedResumeUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>[]
          }
          upsert: {
            args: Prisma.ParsedResumeUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ParsedResumePayload>
          }
          aggregate: {
            args: Prisma.ParsedResumeAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateParsedResume>
          }
          groupBy: {
            args: Prisma.ParsedResumeGroupByArgs<ExtArgs>
            result: $Utils.Optional<ParsedResumeGroupByOutputType>[]
          }
          count: {
            args: Prisma.ParsedResumeCountArgs<ExtArgs>
            result: $Utils.Optional<ParsedResumeCountAggregateOutputType> | number
          }
        }
      }
      ServiceStatus: {
        payload: Prisma.$ServiceStatusPayload<ExtArgs>
        fields: Prisma.ServiceStatusFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ServiceStatusFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ServiceStatusFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>
          }
          findFirst: {
            args: Prisma.ServiceStatusFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ServiceStatusFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>
          }
          findMany: {
            args: Prisma.ServiceStatusFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>[]
          }
          create: {
            args: Prisma.ServiceStatusCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>
          }
          createMany: {
            args: Prisma.ServiceStatusCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ServiceStatusCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>[]
          }
          delete: {
            args: Prisma.ServiceStatusDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>
          }
          update: {
            args: Prisma.ServiceStatusUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>
          }
          deleteMany: {
            args: Prisma.ServiceStatusDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ServiceStatusUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ServiceStatusUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>[]
          }
          upsert: {
            args: Prisma.ServiceStatusUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusPayload>
          }
          aggregate: {
            args: Prisma.ServiceStatusAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateServiceStatus>
          }
          groupBy: {
            args: Prisma.ServiceStatusGroupByArgs<ExtArgs>
            result: $Utils.Optional<ServiceStatusGroupByOutputType>[]
          }
          count: {
            args: Prisma.ServiceStatusCountArgs<ExtArgs>
            result: $Utils.Optional<ServiceStatusCountAggregateOutputType> | number
          }
        }
      }
      ServiceStatusHistory: {
        payload: Prisma.$ServiceStatusHistoryPayload<ExtArgs>
        fields: Prisma.ServiceStatusHistoryFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ServiceStatusHistoryFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ServiceStatusHistoryFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>
          }
          findFirst: {
            args: Prisma.ServiceStatusHistoryFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ServiceStatusHistoryFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>
          }
          findMany: {
            args: Prisma.ServiceStatusHistoryFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>[]
          }
          create: {
            args: Prisma.ServiceStatusHistoryCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>
          }
          createMany: {
            args: Prisma.ServiceStatusHistoryCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ServiceStatusHistoryCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>[]
          }
          delete: {
            args: Prisma.ServiceStatusHistoryDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>
          }
          update: {
            args: Prisma.ServiceStatusHistoryUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>
          }
          deleteMany: {
            args: Prisma.ServiceStatusHistoryDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ServiceStatusHistoryUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ServiceStatusHistoryUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>[]
          }
          upsert: {
            args: Prisma.ServiceStatusHistoryUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ServiceStatusHistoryPayload>
          }
          aggregate: {
            args: Prisma.ServiceStatusHistoryAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateServiceStatusHistory>
          }
          groupBy: {
            args: Prisma.ServiceStatusHistoryGroupByArgs<ExtArgs>
            result: $Utils.Optional<ServiceStatusHistoryGroupByOutputType>[]
          }
          count: {
            args: Prisma.ServiceStatusHistoryCountArgs<ExtArgs>
            result: $Utils.Optional<ServiceStatusHistoryCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    workerProcess?: WorkerProcessOmit
    searchJob?: SearchJobOmit
    atsAnalysis?: AtsAnalysisOmit
    parsedResume?: ParsedResumeOmit
    serviceStatus?: ServiceStatusOmit
    serviceStatusHistory?: ServiceStatusHistoryOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type ServiceStatusCountOutputType
   */

  export type ServiceStatusCountOutputType = {
    statusHistory: number
  }

  export type ServiceStatusCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    statusHistory?: boolean | ServiceStatusCountOutputTypeCountStatusHistoryArgs
  }

  // Custom InputTypes
  /**
   * ServiceStatusCountOutputType without action
   */
  export type ServiceStatusCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusCountOutputType
     */
    select?: ServiceStatusCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ServiceStatusCountOutputType without action
   */
  export type ServiceStatusCountOutputTypeCountStatusHistoryArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ServiceStatusHistoryWhereInput
  }


  /**
   * Models
   */

  /**
   * Model WorkerProcess
   */

  export type AggregateWorkerProcess = {
    _count: WorkerProcessCountAggregateOutputType | null
    _min: WorkerProcessMinAggregateOutputType | null
    _max: WorkerProcessMaxAggregateOutputType | null
  }

  export type WorkerProcessMinAggregateOutputType = {
    id: string | null
    type: string | null
    status: string | null
    createdAt: Date | null
    updatedAt: Date | null
    startedAt: Date | null
    completedAt: Date | null
    error: string | null
  }

  export type WorkerProcessMaxAggregateOutputType = {
    id: string | null
    type: string | null
    status: string | null
    createdAt: Date | null
    updatedAt: Date | null
    startedAt: Date | null
    completedAt: Date | null
    error: string | null
  }

  export type WorkerProcessCountAggregateOutputType = {
    id: number
    type: number
    status: number
    data: number
    createdAt: number
    updatedAt: number
    startedAt: number
    completedAt: number
    error: number
    _all: number
  }


  export type WorkerProcessMinAggregateInputType = {
    id?: true
    type?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    startedAt?: true
    completedAt?: true
    error?: true
  }

  export type WorkerProcessMaxAggregateInputType = {
    id?: true
    type?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    startedAt?: true
    completedAt?: true
    error?: true
  }

  export type WorkerProcessCountAggregateInputType = {
    id?: true
    type?: true
    status?: true
    data?: true
    createdAt?: true
    updatedAt?: true
    startedAt?: true
    completedAt?: true
    error?: true
    _all?: true
  }

  export type WorkerProcessAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which WorkerProcess to aggregate.
     */
    where?: WorkerProcessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of WorkerProcesses to fetch.
     */
    orderBy?: WorkerProcessOrderByWithRelationInput | WorkerProcessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: WorkerProcessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` WorkerProcesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` WorkerProcesses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned WorkerProcesses
    **/
    _count?: true | WorkerProcessCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: WorkerProcessMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: WorkerProcessMaxAggregateInputType
  }

  export type GetWorkerProcessAggregateType<T extends WorkerProcessAggregateArgs> = {
        [P in keyof T & keyof AggregateWorkerProcess]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateWorkerProcess[P]>
      : GetScalarType<T[P], AggregateWorkerProcess[P]>
  }




  export type WorkerProcessGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: WorkerProcessWhereInput
    orderBy?: WorkerProcessOrderByWithAggregationInput | WorkerProcessOrderByWithAggregationInput[]
    by: WorkerProcessScalarFieldEnum[] | WorkerProcessScalarFieldEnum
    having?: WorkerProcessScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: WorkerProcessCountAggregateInputType | true
    _min?: WorkerProcessMinAggregateInputType
    _max?: WorkerProcessMaxAggregateInputType
  }

  export type WorkerProcessGroupByOutputType = {
    id: string
    type: string
    status: string
    data: JsonValue | null
    createdAt: Date
    updatedAt: Date
    startedAt: Date | null
    completedAt: Date | null
    error: string | null
    _count: WorkerProcessCountAggregateOutputType | null
    _min: WorkerProcessMinAggregateOutputType | null
    _max: WorkerProcessMaxAggregateOutputType | null
  }

  type GetWorkerProcessGroupByPayload<T extends WorkerProcessGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<WorkerProcessGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof WorkerProcessGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], WorkerProcessGroupByOutputType[P]>
            : GetScalarType<T[P], WorkerProcessGroupByOutputType[P]>
        }
      >
    >


  export type WorkerProcessSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }, ExtArgs["result"]["workerProcess"]>

  export type WorkerProcessSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }, ExtArgs["result"]["workerProcess"]>

  export type WorkerProcessSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }, ExtArgs["result"]["workerProcess"]>

  export type WorkerProcessSelectScalar = {
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }

  export type WorkerProcessOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "type" | "status" | "data" | "createdAt" | "updatedAt" | "startedAt" | "completedAt" | "error", ExtArgs["result"]["workerProcess"]>

  export type $WorkerProcessPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "WorkerProcess"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      type: string
      status: string
      data: Prisma.JsonValue | null
      createdAt: Date
      updatedAt: Date
      startedAt: Date | null
      completedAt: Date | null
      error: string | null
    }, ExtArgs["result"]["workerProcess"]>
    composites: {}
  }

  type WorkerProcessGetPayload<S extends boolean | null | undefined | WorkerProcessDefaultArgs> = $Result.GetResult<Prisma.$WorkerProcessPayload, S>

  type WorkerProcessCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<WorkerProcessFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: WorkerProcessCountAggregateInputType | true
    }

  export interface WorkerProcessDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['WorkerProcess'], meta: { name: 'WorkerProcess' } }
    /**
     * Find zero or one WorkerProcess that matches the filter.
     * @param {WorkerProcessFindUniqueArgs} args - Arguments to find a WorkerProcess
     * @example
     * // Get one WorkerProcess
     * const workerProcess = await prisma.workerProcess.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends WorkerProcessFindUniqueArgs>(args: SelectSubset<T, WorkerProcessFindUniqueArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one WorkerProcess that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {WorkerProcessFindUniqueOrThrowArgs} args - Arguments to find a WorkerProcess
     * @example
     * // Get one WorkerProcess
     * const workerProcess = await prisma.workerProcess.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends WorkerProcessFindUniqueOrThrowArgs>(args: SelectSubset<T, WorkerProcessFindUniqueOrThrowArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first WorkerProcess that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {WorkerProcessFindFirstArgs} args - Arguments to find a WorkerProcess
     * @example
     * // Get one WorkerProcess
     * const workerProcess = await prisma.workerProcess.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends WorkerProcessFindFirstArgs>(args?: SelectSubset<T, WorkerProcessFindFirstArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first WorkerProcess that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {WorkerProcessFindFirstOrThrowArgs} args - Arguments to find a WorkerProcess
     * @example
     * // Get one WorkerProcess
     * const workerProcess = await prisma.workerProcess.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends WorkerProcessFindFirstOrThrowArgs>(args?: SelectSubset<T, WorkerProcessFindFirstOrThrowArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more WorkerProcesses that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {WorkerProcessFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all WorkerProcesses
     * const workerProcesses = await prisma.workerProcess.findMany()
     * 
     * // Get first 10 WorkerProcesses
     * const workerProcesses = await prisma.workerProcess.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const workerProcessWithIdOnly = await prisma.workerProcess.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends WorkerProcessFindManyArgs>(args?: SelectSubset<T, WorkerProcessFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a WorkerProcess.
     * @param {WorkerProcessCreateArgs} args - Arguments to create a WorkerProcess.
     * @example
     * // Create one WorkerProcess
     * const WorkerProcess = await prisma.workerProcess.create({
     *   data: {
     *     // ... data to create a WorkerProcess
     *   }
     * })
     * 
     */
    create<T extends WorkerProcessCreateArgs>(args: SelectSubset<T, WorkerProcessCreateArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many WorkerProcesses.
     * @param {WorkerProcessCreateManyArgs} args - Arguments to create many WorkerProcesses.
     * @example
     * // Create many WorkerProcesses
     * const workerProcess = await prisma.workerProcess.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends WorkerProcessCreateManyArgs>(args?: SelectSubset<T, WorkerProcessCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many WorkerProcesses and returns the data saved in the database.
     * @param {WorkerProcessCreateManyAndReturnArgs} args - Arguments to create many WorkerProcesses.
     * @example
     * // Create many WorkerProcesses
     * const workerProcess = await prisma.workerProcess.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many WorkerProcesses and only return the `id`
     * const workerProcessWithIdOnly = await prisma.workerProcess.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends WorkerProcessCreateManyAndReturnArgs>(args?: SelectSubset<T, WorkerProcessCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a WorkerProcess.
     * @param {WorkerProcessDeleteArgs} args - Arguments to delete one WorkerProcess.
     * @example
     * // Delete one WorkerProcess
     * const WorkerProcess = await prisma.workerProcess.delete({
     *   where: {
     *     // ... filter to delete one WorkerProcess
     *   }
     * })
     * 
     */
    delete<T extends WorkerProcessDeleteArgs>(args: SelectSubset<T, WorkerProcessDeleteArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one WorkerProcess.
     * @param {WorkerProcessUpdateArgs} args - Arguments to update one WorkerProcess.
     * @example
     * // Update one WorkerProcess
     * const workerProcess = await prisma.workerProcess.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends WorkerProcessUpdateArgs>(args: SelectSubset<T, WorkerProcessUpdateArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more WorkerProcesses.
     * @param {WorkerProcessDeleteManyArgs} args - Arguments to filter WorkerProcesses to delete.
     * @example
     * // Delete a few WorkerProcesses
     * const { count } = await prisma.workerProcess.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends WorkerProcessDeleteManyArgs>(args?: SelectSubset<T, WorkerProcessDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more WorkerProcesses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {WorkerProcessUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many WorkerProcesses
     * const workerProcess = await prisma.workerProcess.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends WorkerProcessUpdateManyArgs>(args: SelectSubset<T, WorkerProcessUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more WorkerProcesses and returns the data updated in the database.
     * @param {WorkerProcessUpdateManyAndReturnArgs} args - Arguments to update many WorkerProcesses.
     * @example
     * // Update many WorkerProcesses
     * const workerProcess = await prisma.workerProcess.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more WorkerProcesses and only return the `id`
     * const workerProcessWithIdOnly = await prisma.workerProcess.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends WorkerProcessUpdateManyAndReturnArgs>(args: SelectSubset<T, WorkerProcessUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one WorkerProcess.
     * @param {WorkerProcessUpsertArgs} args - Arguments to update or create a WorkerProcess.
     * @example
     * // Update or create a WorkerProcess
     * const workerProcess = await prisma.workerProcess.upsert({
     *   create: {
     *     // ... data to create a WorkerProcess
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the WorkerProcess we want to update
     *   }
     * })
     */
    upsert<T extends WorkerProcessUpsertArgs>(args: SelectSubset<T, WorkerProcessUpsertArgs<ExtArgs>>): Prisma__WorkerProcessClient<$Result.GetResult<Prisma.$WorkerProcessPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of WorkerProcesses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {WorkerProcessCountArgs} args - Arguments to filter WorkerProcesses to count.
     * @example
     * // Count the number of WorkerProcesses
     * const count = await prisma.workerProcess.count({
     *   where: {
     *     // ... the filter for the WorkerProcesses we want to count
     *   }
     * })
    **/
    count<T extends WorkerProcessCountArgs>(
      args?: Subset<T, WorkerProcessCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], WorkerProcessCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a WorkerProcess.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {WorkerProcessAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends WorkerProcessAggregateArgs>(args: Subset<T, WorkerProcessAggregateArgs>): Prisma.PrismaPromise<GetWorkerProcessAggregateType<T>>

    /**
     * Group by WorkerProcess.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {WorkerProcessGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends WorkerProcessGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: WorkerProcessGroupByArgs['orderBy'] }
        : { orderBy?: WorkerProcessGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, WorkerProcessGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetWorkerProcessGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the WorkerProcess model
   */
  readonly fields: WorkerProcessFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for WorkerProcess.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__WorkerProcessClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the WorkerProcess model
   */
  interface WorkerProcessFieldRefs {
    readonly id: FieldRef<"WorkerProcess", 'String'>
    readonly type: FieldRef<"WorkerProcess", 'String'>
    readonly status: FieldRef<"WorkerProcess", 'String'>
    readonly data: FieldRef<"WorkerProcess", 'Json'>
    readonly createdAt: FieldRef<"WorkerProcess", 'DateTime'>
    readonly updatedAt: FieldRef<"WorkerProcess", 'DateTime'>
    readonly startedAt: FieldRef<"WorkerProcess", 'DateTime'>
    readonly completedAt: FieldRef<"WorkerProcess", 'DateTime'>
    readonly error: FieldRef<"WorkerProcess", 'String'>
  }
    

  // Custom InputTypes
  /**
   * WorkerProcess findUnique
   */
  export type WorkerProcessFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * Filter, which WorkerProcess to fetch.
     */
    where: WorkerProcessWhereUniqueInput
  }

  /**
   * WorkerProcess findUniqueOrThrow
   */
  export type WorkerProcessFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * Filter, which WorkerProcess to fetch.
     */
    where: WorkerProcessWhereUniqueInput
  }

  /**
   * WorkerProcess findFirst
   */
  export type WorkerProcessFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * Filter, which WorkerProcess to fetch.
     */
    where?: WorkerProcessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of WorkerProcesses to fetch.
     */
    orderBy?: WorkerProcessOrderByWithRelationInput | WorkerProcessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for WorkerProcesses.
     */
    cursor?: WorkerProcessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` WorkerProcesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` WorkerProcesses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of WorkerProcesses.
     */
    distinct?: WorkerProcessScalarFieldEnum | WorkerProcessScalarFieldEnum[]
  }

  /**
   * WorkerProcess findFirstOrThrow
   */
  export type WorkerProcessFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * Filter, which WorkerProcess to fetch.
     */
    where?: WorkerProcessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of WorkerProcesses to fetch.
     */
    orderBy?: WorkerProcessOrderByWithRelationInput | WorkerProcessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for WorkerProcesses.
     */
    cursor?: WorkerProcessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` WorkerProcesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` WorkerProcesses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of WorkerProcesses.
     */
    distinct?: WorkerProcessScalarFieldEnum | WorkerProcessScalarFieldEnum[]
  }

  /**
   * WorkerProcess findMany
   */
  export type WorkerProcessFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * Filter, which WorkerProcesses to fetch.
     */
    where?: WorkerProcessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of WorkerProcesses to fetch.
     */
    orderBy?: WorkerProcessOrderByWithRelationInput | WorkerProcessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing WorkerProcesses.
     */
    cursor?: WorkerProcessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` WorkerProcesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` WorkerProcesses.
     */
    skip?: number
    distinct?: WorkerProcessScalarFieldEnum | WorkerProcessScalarFieldEnum[]
  }

  /**
   * WorkerProcess create
   */
  export type WorkerProcessCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * The data needed to create a WorkerProcess.
     */
    data: XOR<WorkerProcessCreateInput, WorkerProcessUncheckedCreateInput>
  }

  /**
   * WorkerProcess createMany
   */
  export type WorkerProcessCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many WorkerProcesses.
     */
    data: WorkerProcessCreateManyInput | WorkerProcessCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * WorkerProcess createManyAndReturn
   */
  export type WorkerProcessCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * The data used to create many WorkerProcesses.
     */
    data: WorkerProcessCreateManyInput | WorkerProcessCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * WorkerProcess update
   */
  export type WorkerProcessUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * The data needed to update a WorkerProcess.
     */
    data: XOR<WorkerProcessUpdateInput, WorkerProcessUncheckedUpdateInput>
    /**
     * Choose, which WorkerProcess to update.
     */
    where: WorkerProcessWhereUniqueInput
  }

  /**
   * WorkerProcess updateMany
   */
  export type WorkerProcessUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update WorkerProcesses.
     */
    data: XOR<WorkerProcessUpdateManyMutationInput, WorkerProcessUncheckedUpdateManyInput>
    /**
     * Filter which WorkerProcesses to update
     */
    where?: WorkerProcessWhereInput
    /**
     * Limit how many WorkerProcesses to update.
     */
    limit?: number
  }

  /**
   * WorkerProcess updateManyAndReturn
   */
  export type WorkerProcessUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * The data used to update WorkerProcesses.
     */
    data: XOR<WorkerProcessUpdateManyMutationInput, WorkerProcessUncheckedUpdateManyInput>
    /**
     * Filter which WorkerProcesses to update
     */
    where?: WorkerProcessWhereInput
    /**
     * Limit how many WorkerProcesses to update.
     */
    limit?: number
  }

  /**
   * WorkerProcess upsert
   */
  export type WorkerProcessUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * The filter to search for the WorkerProcess to update in case it exists.
     */
    where: WorkerProcessWhereUniqueInput
    /**
     * In case the WorkerProcess found by the `where` argument doesn't exist, create a new WorkerProcess with this data.
     */
    create: XOR<WorkerProcessCreateInput, WorkerProcessUncheckedCreateInput>
    /**
     * In case the WorkerProcess was found with the provided `where` argument, update it with this data.
     */
    update: XOR<WorkerProcessUpdateInput, WorkerProcessUncheckedUpdateInput>
  }

  /**
   * WorkerProcess delete
   */
  export type WorkerProcessDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
    /**
     * Filter which WorkerProcess to delete.
     */
    where: WorkerProcessWhereUniqueInput
  }

  /**
   * WorkerProcess deleteMany
   */
  export type WorkerProcessDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which WorkerProcesses to delete
     */
    where?: WorkerProcessWhereInput
    /**
     * Limit how many WorkerProcesses to delete.
     */
    limit?: number
  }

  /**
   * WorkerProcess without action
   */
  export type WorkerProcessDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the WorkerProcess
     */
    select?: WorkerProcessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the WorkerProcess
     */
    omit?: WorkerProcessOmit<ExtArgs> | null
  }


  /**
   * Model SearchJob
   */

  export type AggregateSearchJob = {
    _count: SearchJobCountAggregateOutputType | null
    _min: SearchJobMinAggregateOutputType | null
    _max: SearchJobMaxAggregateOutputType | null
  }

  export type SearchJobMinAggregateOutputType = {
    id: string | null
    query: string | null
    status: string | null
    createdAt: Date | null
    updatedAt: Date | null
    startedAt: Date | null
    completedAt: Date | null
    error: string | null
  }

  export type SearchJobMaxAggregateOutputType = {
    id: string | null
    query: string | null
    status: string | null
    createdAt: Date | null
    updatedAt: Date | null
    startedAt: Date | null
    completedAt: Date | null
    error: string | null
  }

  export type SearchJobCountAggregateOutputType = {
    id: number
    query: number
    filters: number
    status: number
    results: number
    createdAt: number
    updatedAt: number
    startedAt: number
    completedAt: number
    error: number
    _all: number
  }


  export type SearchJobMinAggregateInputType = {
    id?: true
    query?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    startedAt?: true
    completedAt?: true
    error?: true
  }

  export type SearchJobMaxAggregateInputType = {
    id?: true
    query?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    startedAt?: true
    completedAt?: true
    error?: true
  }

  export type SearchJobCountAggregateInputType = {
    id?: true
    query?: true
    filters?: true
    status?: true
    results?: true
    createdAt?: true
    updatedAt?: true
    startedAt?: true
    completedAt?: true
    error?: true
    _all?: true
  }

  export type SearchJobAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SearchJob to aggregate.
     */
    where?: SearchJobWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchJobs to fetch.
     */
    orderBy?: SearchJobOrderByWithRelationInput | SearchJobOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: SearchJobWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchJobs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchJobs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned SearchJobs
    **/
    _count?: true | SearchJobCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: SearchJobMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: SearchJobMaxAggregateInputType
  }

  export type GetSearchJobAggregateType<T extends SearchJobAggregateArgs> = {
        [P in keyof T & keyof AggregateSearchJob]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateSearchJob[P]>
      : GetScalarType<T[P], AggregateSearchJob[P]>
  }




  export type SearchJobGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchJobWhereInput
    orderBy?: SearchJobOrderByWithAggregationInput | SearchJobOrderByWithAggregationInput[]
    by: SearchJobScalarFieldEnum[] | SearchJobScalarFieldEnum
    having?: SearchJobScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: SearchJobCountAggregateInputType | true
    _min?: SearchJobMinAggregateInputType
    _max?: SearchJobMaxAggregateInputType
  }

  export type SearchJobGroupByOutputType = {
    id: string
    query: string
    filters: JsonValue | null
    status: string
    results: JsonValue | null
    createdAt: Date
    updatedAt: Date
    startedAt: Date | null
    completedAt: Date | null
    error: string | null
    _count: SearchJobCountAggregateOutputType | null
    _min: SearchJobMinAggregateOutputType | null
    _max: SearchJobMaxAggregateOutputType | null
  }

  type GetSearchJobGroupByPayload<T extends SearchJobGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<SearchJobGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof SearchJobGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], SearchJobGroupByOutputType[P]>
            : GetScalarType<T[P], SearchJobGroupByOutputType[P]>
        }
      >
    >


  export type SearchJobSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    query?: boolean
    filters?: boolean
    status?: boolean
    results?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }, ExtArgs["result"]["searchJob"]>

  export type SearchJobSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    query?: boolean
    filters?: boolean
    status?: boolean
    results?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }, ExtArgs["result"]["searchJob"]>

  export type SearchJobSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    query?: boolean
    filters?: boolean
    status?: boolean
    results?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }, ExtArgs["result"]["searchJob"]>

  export type SearchJobSelectScalar = {
    id?: boolean
    query?: boolean
    filters?: boolean
    status?: boolean
    results?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    startedAt?: boolean
    completedAt?: boolean
    error?: boolean
  }

  export type SearchJobOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "query" | "filters" | "status" | "results" | "createdAt" | "updatedAt" | "startedAt" | "completedAt" | "error", ExtArgs["result"]["searchJob"]>

  export type $SearchJobPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "SearchJob"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      query: string
      filters: Prisma.JsonValue | null
      status: string
      results: Prisma.JsonValue | null
      createdAt: Date
      updatedAt: Date
      startedAt: Date | null
      completedAt: Date | null
      error: string | null
    }, ExtArgs["result"]["searchJob"]>
    composites: {}
  }

  type SearchJobGetPayload<S extends boolean | null | undefined | SearchJobDefaultArgs> = $Result.GetResult<Prisma.$SearchJobPayload, S>

  type SearchJobCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<SearchJobFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: SearchJobCountAggregateInputType | true
    }

  export interface SearchJobDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['SearchJob'], meta: { name: 'SearchJob' } }
    /**
     * Find zero or one SearchJob that matches the filter.
     * @param {SearchJobFindUniqueArgs} args - Arguments to find a SearchJob
     * @example
     * // Get one SearchJob
     * const searchJob = await prisma.searchJob.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends SearchJobFindUniqueArgs>(args: SelectSubset<T, SearchJobFindUniqueArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one SearchJob that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {SearchJobFindUniqueOrThrowArgs} args - Arguments to find a SearchJob
     * @example
     * // Get one SearchJob
     * const searchJob = await prisma.searchJob.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends SearchJobFindUniqueOrThrowArgs>(args: SelectSubset<T, SearchJobFindUniqueOrThrowArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SearchJob that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchJobFindFirstArgs} args - Arguments to find a SearchJob
     * @example
     * // Get one SearchJob
     * const searchJob = await prisma.searchJob.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends SearchJobFindFirstArgs>(args?: SelectSubset<T, SearchJobFindFirstArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SearchJob that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchJobFindFirstOrThrowArgs} args - Arguments to find a SearchJob
     * @example
     * // Get one SearchJob
     * const searchJob = await prisma.searchJob.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends SearchJobFindFirstOrThrowArgs>(args?: SelectSubset<T, SearchJobFindFirstOrThrowArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more SearchJobs that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchJobFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all SearchJobs
     * const searchJobs = await prisma.searchJob.findMany()
     * 
     * // Get first 10 SearchJobs
     * const searchJobs = await prisma.searchJob.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const searchJobWithIdOnly = await prisma.searchJob.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends SearchJobFindManyArgs>(args?: SelectSubset<T, SearchJobFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a SearchJob.
     * @param {SearchJobCreateArgs} args - Arguments to create a SearchJob.
     * @example
     * // Create one SearchJob
     * const SearchJob = await prisma.searchJob.create({
     *   data: {
     *     // ... data to create a SearchJob
     *   }
     * })
     * 
     */
    create<T extends SearchJobCreateArgs>(args: SelectSubset<T, SearchJobCreateArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many SearchJobs.
     * @param {SearchJobCreateManyArgs} args - Arguments to create many SearchJobs.
     * @example
     * // Create many SearchJobs
     * const searchJob = await prisma.searchJob.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends SearchJobCreateManyArgs>(args?: SelectSubset<T, SearchJobCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many SearchJobs and returns the data saved in the database.
     * @param {SearchJobCreateManyAndReturnArgs} args - Arguments to create many SearchJobs.
     * @example
     * // Create many SearchJobs
     * const searchJob = await prisma.searchJob.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many SearchJobs and only return the `id`
     * const searchJobWithIdOnly = await prisma.searchJob.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends SearchJobCreateManyAndReturnArgs>(args?: SelectSubset<T, SearchJobCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a SearchJob.
     * @param {SearchJobDeleteArgs} args - Arguments to delete one SearchJob.
     * @example
     * // Delete one SearchJob
     * const SearchJob = await prisma.searchJob.delete({
     *   where: {
     *     // ... filter to delete one SearchJob
     *   }
     * })
     * 
     */
    delete<T extends SearchJobDeleteArgs>(args: SelectSubset<T, SearchJobDeleteArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one SearchJob.
     * @param {SearchJobUpdateArgs} args - Arguments to update one SearchJob.
     * @example
     * // Update one SearchJob
     * const searchJob = await prisma.searchJob.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends SearchJobUpdateArgs>(args: SelectSubset<T, SearchJobUpdateArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more SearchJobs.
     * @param {SearchJobDeleteManyArgs} args - Arguments to filter SearchJobs to delete.
     * @example
     * // Delete a few SearchJobs
     * const { count } = await prisma.searchJob.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends SearchJobDeleteManyArgs>(args?: SelectSubset<T, SearchJobDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more SearchJobs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchJobUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many SearchJobs
     * const searchJob = await prisma.searchJob.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends SearchJobUpdateManyArgs>(args: SelectSubset<T, SearchJobUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more SearchJobs and returns the data updated in the database.
     * @param {SearchJobUpdateManyAndReturnArgs} args - Arguments to update many SearchJobs.
     * @example
     * // Update many SearchJobs
     * const searchJob = await prisma.searchJob.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more SearchJobs and only return the `id`
     * const searchJobWithIdOnly = await prisma.searchJob.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends SearchJobUpdateManyAndReturnArgs>(args: SelectSubset<T, SearchJobUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one SearchJob.
     * @param {SearchJobUpsertArgs} args - Arguments to update or create a SearchJob.
     * @example
     * // Update or create a SearchJob
     * const searchJob = await prisma.searchJob.upsert({
     *   create: {
     *     // ... data to create a SearchJob
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the SearchJob we want to update
     *   }
     * })
     */
    upsert<T extends SearchJobUpsertArgs>(args: SelectSubset<T, SearchJobUpsertArgs<ExtArgs>>): Prisma__SearchJobClient<$Result.GetResult<Prisma.$SearchJobPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of SearchJobs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchJobCountArgs} args - Arguments to filter SearchJobs to count.
     * @example
     * // Count the number of SearchJobs
     * const count = await prisma.searchJob.count({
     *   where: {
     *     // ... the filter for the SearchJobs we want to count
     *   }
     * })
    **/
    count<T extends SearchJobCountArgs>(
      args?: Subset<T, SearchJobCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], SearchJobCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a SearchJob.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchJobAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends SearchJobAggregateArgs>(args: Subset<T, SearchJobAggregateArgs>): Prisma.PrismaPromise<GetSearchJobAggregateType<T>>

    /**
     * Group by SearchJob.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchJobGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends SearchJobGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: SearchJobGroupByArgs['orderBy'] }
        : { orderBy?: SearchJobGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, SearchJobGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSearchJobGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SearchJob model
   */
  readonly fields: SearchJobFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for SearchJob.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__SearchJobClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the SearchJob model
   */
  interface SearchJobFieldRefs {
    readonly id: FieldRef<"SearchJob", 'String'>
    readonly query: FieldRef<"SearchJob", 'String'>
    readonly filters: FieldRef<"SearchJob", 'Json'>
    readonly status: FieldRef<"SearchJob", 'String'>
    readonly results: FieldRef<"SearchJob", 'Json'>
    readonly createdAt: FieldRef<"SearchJob", 'DateTime'>
    readonly updatedAt: FieldRef<"SearchJob", 'DateTime'>
    readonly startedAt: FieldRef<"SearchJob", 'DateTime'>
    readonly completedAt: FieldRef<"SearchJob", 'DateTime'>
    readonly error: FieldRef<"SearchJob", 'String'>
  }
    

  // Custom InputTypes
  /**
   * SearchJob findUnique
   */
  export type SearchJobFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * Filter, which SearchJob to fetch.
     */
    where: SearchJobWhereUniqueInput
  }

  /**
   * SearchJob findUniqueOrThrow
   */
  export type SearchJobFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * Filter, which SearchJob to fetch.
     */
    where: SearchJobWhereUniqueInput
  }

  /**
   * SearchJob findFirst
   */
  export type SearchJobFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * Filter, which SearchJob to fetch.
     */
    where?: SearchJobWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchJobs to fetch.
     */
    orderBy?: SearchJobOrderByWithRelationInput | SearchJobOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SearchJobs.
     */
    cursor?: SearchJobWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchJobs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchJobs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SearchJobs.
     */
    distinct?: SearchJobScalarFieldEnum | SearchJobScalarFieldEnum[]
  }

  /**
   * SearchJob findFirstOrThrow
   */
  export type SearchJobFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * Filter, which SearchJob to fetch.
     */
    where?: SearchJobWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchJobs to fetch.
     */
    orderBy?: SearchJobOrderByWithRelationInput | SearchJobOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SearchJobs.
     */
    cursor?: SearchJobWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchJobs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchJobs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SearchJobs.
     */
    distinct?: SearchJobScalarFieldEnum | SearchJobScalarFieldEnum[]
  }

  /**
   * SearchJob findMany
   */
  export type SearchJobFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * Filter, which SearchJobs to fetch.
     */
    where?: SearchJobWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchJobs to fetch.
     */
    orderBy?: SearchJobOrderByWithRelationInput | SearchJobOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing SearchJobs.
     */
    cursor?: SearchJobWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchJobs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchJobs.
     */
    skip?: number
    distinct?: SearchJobScalarFieldEnum | SearchJobScalarFieldEnum[]
  }

  /**
   * SearchJob create
   */
  export type SearchJobCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * The data needed to create a SearchJob.
     */
    data: XOR<SearchJobCreateInput, SearchJobUncheckedCreateInput>
  }

  /**
   * SearchJob createMany
   */
  export type SearchJobCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many SearchJobs.
     */
    data: SearchJobCreateManyInput | SearchJobCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * SearchJob createManyAndReturn
   */
  export type SearchJobCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * The data used to create many SearchJobs.
     */
    data: SearchJobCreateManyInput | SearchJobCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * SearchJob update
   */
  export type SearchJobUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * The data needed to update a SearchJob.
     */
    data: XOR<SearchJobUpdateInput, SearchJobUncheckedUpdateInput>
    /**
     * Choose, which SearchJob to update.
     */
    where: SearchJobWhereUniqueInput
  }

  /**
   * SearchJob updateMany
   */
  export type SearchJobUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update SearchJobs.
     */
    data: XOR<SearchJobUpdateManyMutationInput, SearchJobUncheckedUpdateManyInput>
    /**
     * Filter which SearchJobs to update
     */
    where?: SearchJobWhereInput
    /**
     * Limit how many SearchJobs to update.
     */
    limit?: number
  }

  /**
   * SearchJob updateManyAndReturn
   */
  export type SearchJobUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * The data used to update SearchJobs.
     */
    data: XOR<SearchJobUpdateManyMutationInput, SearchJobUncheckedUpdateManyInput>
    /**
     * Filter which SearchJobs to update
     */
    where?: SearchJobWhereInput
    /**
     * Limit how many SearchJobs to update.
     */
    limit?: number
  }

  /**
   * SearchJob upsert
   */
  export type SearchJobUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * The filter to search for the SearchJob to update in case it exists.
     */
    where: SearchJobWhereUniqueInput
    /**
     * In case the SearchJob found by the `where` argument doesn't exist, create a new SearchJob with this data.
     */
    create: XOR<SearchJobCreateInput, SearchJobUncheckedCreateInput>
    /**
     * In case the SearchJob was found with the provided `where` argument, update it with this data.
     */
    update: XOR<SearchJobUpdateInput, SearchJobUncheckedUpdateInput>
  }

  /**
   * SearchJob delete
   */
  export type SearchJobDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
    /**
     * Filter which SearchJob to delete.
     */
    where: SearchJobWhereUniqueInput
  }

  /**
   * SearchJob deleteMany
   */
  export type SearchJobDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SearchJobs to delete
     */
    where?: SearchJobWhereInput
    /**
     * Limit how many SearchJobs to delete.
     */
    limit?: number
  }

  /**
   * SearchJob without action
   */
  export type SearchJobDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchJob
     */
    select?: SearchJobSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchJob
     */
    omit?: SearchJobOmit<ExtArgs> | null
  }


  /**
   * Model AtsAnalysis
   */

  export type AggregateAtsAnalysis = {
    _count: AtsAnalysisCountAggregateOutputType | null
    _avg: AtsAnalysisAvgAggregateOutputType | null
    _sum: AtsAnalysisSumAggregateOutputType | null
    _min: AtsAnalysisMinAggregateOutputType | null
    _max: AtsAnalysisMaxAggregateOutputType | null
  }

  export type AtsAnalysisAvgAggregateOutputType = {
    overallScore: number | null
    keywordScore: number | null
    formatScore: number | null
    contentScore: number | null
    readabilityScore: number | null
  }

  export type AtsAnalysisSumAggregateOutputType = {
    overallScore: number | null
    keywordScore: number | null
    formatScore: number | null
    contentScore: number | null
    readabilityScore: number | null
  }

  export type AtsAnalysisMinAggregateOutputType = {
    id: string | null
    resumeId: string | null
    overallScore: number | null
    keywordScore: number | null
    formatScore: number | null
    contentScore: number | null
    readabilityScore: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AtsAnalysisMaxAggregateOutputType = {
    id: string | null
    resumeId: string | null
    overallScore: number | null
    keywordScore: number | null
    formatScore: number | null
    contentScore: number | null
    readabilityScore: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AtsAnalysisCountAggregateOutputType = {
    id: number
    resumeId: number
    overallScore: number
    keywordScore: number
    formatScore: number
    contentScore: number
    readabilityScore: number
    detectedIssues: number
    suggestedKeywords: number
    analysisDetails: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type AtsAnalysisAvgAggregateInputType = {
    overallScore?: true
    keywordScore?: true
    formatScore?: true
    contentScore?: true
    readabilityScore?: true
  }

  export type AtsAnalysisSumAggregateInputType = {
    overallScore?: true
    keywordScore?: true
    formatScore?: true
    contentScore?: true
    readabilityScore?: true
  }

  export type AtsAnalysisMinAggregateInputType = {
    id?: true
    resumeId?: true
    overallScore?: true
    keywordScore?: true
    formatScore?: true
    contentScore?: true
    readabilityScore?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AtsAnalysisMaxAggregateInputType = {
    id?: true
    resumeId?: true
    overallScore?: true
    keywordScore?: true
    formatScore?: true
    contentScore?: true
    readabilityScore?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AtsAnalysisCountAggregateInputType = {
    id?: true
    resumeId?: true
    overallScore?: true
    keywordScore?: true
    formatScore?: true
    contentScore?: true
    readabilityScore?: true
    detectedIssues?: true
    suggestedKeywords?: true
    analysisDetails?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type AtsAnalysisAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AtsAnalysis to aggregate.
     */
    where?: AtsAnalysisWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AtsAnalyses to fetch.
     */
    orderBy?: AtsAnalysisOrderByWithRelationInput | AtsAnalysisOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AtsAnalysisWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AtsAnalyses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AtsAnalyses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned AtsAnalyses
    **/
    _count?: true | AtsAnalysisCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: AtsAnalysisAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: AtsAnalysisSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AtsAnalysisMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AtsAnalysisMaxAggregateInputType
  }

  export type GetAtsAnalysisAggregateType<T extends AtsAnalysisAggregateArgs> = {
        [P in keyof T & keyof AggregateAtsAnalysis]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAtsAnalysis[P]>
      : GetScalarType<T[P], AggregateAtsAnalysis[P]>
  }




  export type AtsAnalysisGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AtsAnalysisWhereInput
    orderBy?: AtsAnalysisOrderByWithAggregationInput | AtsAnalysisOrderByWithAggregationInput[]
    by: AtsAnalysisScalarFieldEnum[] | AtsAnalysisScalarFieldEnum
    having?: AtsAnalysisScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AtsAnalysisCountAggregateInputType | true
    _avg?: AtsAnalysisAvgAggregateInputType
    _sum?: AtsAnalysisSumAggregateInputType
    _min?: AtsAnalysisMinAggregateInputType
    _max?: AtsAnalysisMaxAggregateInputType
  }

  export type AtsAnalysisGroupByOutputType = {
    id: string
    resumeId: string
    overallScore: number
    keywordScore: number
    formatScore: number
    contentScore: number
    readabilityScore: number
    detectedIssues: JsonValue
    suggestedKeywords: JsonValue
    analysisDetails: JsonValue | null
    createdAt: Date
    updatedAt: Date
    _count: AtsAnalysisCountAggregateOutputType | null
    _avg: AtsAnalysisAvgAggregateOutputType | null
    _sum: AtsAnalysisSumAggregateOutputType | null
    _min: AtsAnalysisMinAggregateOutputType | null
    _max: AtsAnalysisMaxAggregateOutputType | null
  }

  type GetAtsAnalysisGroupByPayload<T extends AtsAnalysisGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AtsAnalysisGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AtsAnalysisGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AtsAnalysisGroupByOutputType[P]>
            : GetScalarType<T[P], AtsAnalysisGroupByOutputType[P]>
        }
      >
    >


  export type AtsAnalysisSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    resumeId?: boolean
    overallScore?: boolean
    keywordScore?: boolean
    formatScore?: boolean
    contentScore?: boolean
    readabilityScore?: boolean
    detectedIssues?: boolean
    suggestedKeywords?: boolean
    analysisDetails?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["atsAnalysis"]>

  export type AtsAnalysisSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    resumeId?: boolean
    overallScore?: boolean
    keywordScore?: boolean
    formatScore?: boolean
    contentScore?: boolean
    readabilityScore?: boolean
    detectedIssues?: boolean
    suggestedKeywords?: boolean
    analysisDetails?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["atsAnalysis"]>

  export type AtsAnalysisSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    resumeId?: boolean
    overallScore?: boolean
    keywordScore?: boolean
    formatScore?: boolean
    contentScore?: boolean
    readabilityScore?: boolean
    detectedIssues?: boolean
    suggestedKeywords?: boolean
    analysisDetails?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["atsAnalysis"]>

  export type AtsAnalysisSelectScalar = {
    id?: boolean
    resumeId?: boolean
    overallScore?: boolean
    keywordScore?: boolean
    formatScore?: boolean
    contentScore?: boolean
    readabilityScore?: boolean
    detectedIssues?: boolean
    suggestedKeywords?: boolean
    analysisDetails?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type AtsAnalysisOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "resumeId" | "overallScore" | "keywordScore" | "formatScore" | "contentScore" | "readabilityScore" | "detectedIssues" | "suggestedKeywords" | "analysisDetails" | "createdAt" | "updatedAt", ExtArgs["result"]["atsAnalysis"]>

  export type $AtsAnalysisPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "AtsAnalysis"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      resumeId: string
      overallScore: number
      keywordScore: number
      formatScore: number
      contentScore: number
      readabilityScore: number
      detectedIssues: Prisma.JsonValue
      suggestedKeywords: Prisma.JsonValue
      analysisDetails: Prisma.JsonValue | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["atsAnalysis"]>
    composites: {}
  }

  type AtsAnalysisGetPayload<S extends boolean | null | undefined | AtsAnalysisDefaultArgs> = $Result.GetResult<Prisma.$AtsAnalysisPayload, S>

  type AtsAnalysisCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AtsAnalysisFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AtsAnalysisCountAggregateInputType | true
    }

  export interface AtsAnalysisDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AtsAnalysis'], meta: { name: 'AtsAnalysis' } }
    /**
     * Find zero or one AtsAnalysis that matches the filter.
     * @param {AtsAnalysisFindUniqueArgs} args - Arguments to find a AtsAnalysis
     * @example
     * // Get one AtsAnalysis
     * const atsAnalysis = await prisma.atsAnalysis.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AtsAnalysisFindUniqueArgs>(args: SelectSubset<T, AtsAnalysisFindUniqueArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one AtsAnalysis that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AtsAnalysisFindUniqueOrThrowArgs} args - Arguments to find a AtsAnalysis
     * @example
     * // Get one AtsAnalysis
     * const atsAnalysis = await prisma.atsAnalysis.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AtsAnalysisFindUniqueOrThrowArgs>(args: SelectSubset<T, AtsAnalysisFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AtsAnalysis that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AtsAnalysisFindFirstArgs} args - Arguments to find a AtsAnalysis
     * @example
     * // Get one AtsAnalysis
     * const atsAnalysis = await prisma.atsAnalysis.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AtsAnalysisFindFirstArgs>(args?: SelectSubset<T, AtsAnalysisFindFirstArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AtsAnalysis that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AtsAnalysisFindFirstOrThrowArgs} args - Arguments to find a AtsAnalysis
     * @example
     * // Get one AtsAnalysis
     * const atsAnalysis = await prisma.atsAnalysis.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AtsAnalysisFindFirstOrThrowArgs>(args?: SelectSubset<T, AtsAnalysisFindFirstOrThrowArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more AtsAnalyses that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AtsAnalysisFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all AtsAnalyses
     * const atsAnalyses = await prisma.atsAnalysis.findMany()
     * 
     * // Get first 10 AtsAnalyses
     * const atsAnalyses = await prisma.atsAnalysis.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const atsAnalysisWithIdOnly = await prisma.atsAnalysis.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AtsAnalysisFindManyArgs>(args?: SelectSubset<T, AtsAnalysisFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a AtsAnalysis.
     * @param {AtsAnalysisCreateArgs} args - Arguments to create a AtsAnalysis.
     * @example
     * // Create one AtsAnalysis
     * const AtsAnalysis = await prisma.atsAnalysis.create({
     *   data: {
     *     // ... data to create a AtsAnalysis
     *   }
     * })
     * 
     */
    create<T extends AtsAnalysisCreateArgs>(args: SelectSubset<T, AtsAnalysisCreateArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many AtsAnalyses.
     * @param {AtsAnalysisCreateManyArgs} args - Arguments to create many AtsAnalyses.
     * @example
     * // Create many AtsAnalyses
     * const atsAnalysis = await prisma.atsAnalysis.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AtsAnalysisCreateManyArgs>(args?: SelectSubset<T, AtsAnalysisCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many AtsAnalyses and returns the data saved in the database.
     * @param {AtsAnalysisCreateManyAndReturnArgs} args - Arguments to create many AtsAnalyses.
     * @example
     * // Create many AtsAnalyses
     * const atsAnalysis = await prisma.atsAnalysis.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many AtsAnalyses and only return the `id`
     * const atsAnalysisWithIdOnly = await prisma.atsAnalysis.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AtsAnalysisCreateManyAndReturnArgs>(args?: SelectSubset<T, AtsAnalysisCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a AtsAnalysis.
     * @param {AtsAnalysisDeleteArgs} args - Arguments to delete one AtsAnalysis.
     * @example
     * // Delete one AtsAnalysis
     * const AtsAnalysis = await prisma.atsAnalysis.delete({
     *   where: {
     *     // ... filter to delete one AtsAnalysis
     *   }
     * })
     * 
     */
    delete<T extends AtsAnalysisDeleteArgs>(args: SelectSubset<T, AtsAnalysisDeleteArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one AtsAnalysis.
     * @param {AtsAnalysisUpdateArgs} args - Arguments to update one AtsAnalysis.
     * @example
     * // Update one AtsAnalysis
     * const atsAnalysis = await prisma.atsAnalysis.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AtsAnalysisUpdateArgs>(args: SelectSubset<T, AtsAnalysisUpdateArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more AtsAnalyses.
     * @param {AtsAnalysisDeleteManyArgs} args - Arguments to filter AtsAnalyses to delete.
     * @example
     * // Delete a few AtsAnalyses
     * const { count } = await prisma.atsAnalysis.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AtsAnalysisDeleteManyArgs>(args?: SelectSubset<T, AtsAnalysisDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AtsAnalyses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AtsAnalysisUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many AtsAnalyses
     * const atsAnalysis = await prisma.atsAnalysis.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AtsAnalysisUpdateManyArgs>(args: SelectSubset<T, AtsAnalysisUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AtsAnalyses and returns the data updated in the database.
     * @param {AtsAnalysisUpdateManyAndReturnArgs} args - Arguments to update many AtsAnalyses.
     * @example
     * // Update many AtsAnalyses
     * const atsAnalysis = await prisma.atsAnalysis.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more AtsAnalyses and only return the `id`
     * const atsAnalysisWithIdOnly = await prisma.atsAnalysis.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AtsAnalysisUpdateManyAndReturnArgs>(args: SelectSubset<T, AtsAnalysisUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one AtsAnalysis.
     * @param {AtsAnalysisUpsertArgs} args - Arguments to update or create a AtsAnalysis.
     * @example
     * // Update or create a AtsAnalysis
     * const atsAnalysis = await prisma.atsAnalysis.upsert({
     *   create: {
     *     // ... data to create a AtsAnalysis
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the AtsAnalysis we want to update
     *   }
     * })
     */
    upsert<T extends AtsAnalysisUpsertArgs>(args: SelectSubset<T, AtsAnalysisUpsertArgs<ExtArgs>>): Prisma__AtsAnalysisClient<$Result.GetResult<Prisma.$AtsAnalysisPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of AtsAnalyses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AtsAnalysisCountArgs} args - Arguments to filter AtsAnalyses to count.
     * @example
     * // Count the number of AtsAnalyses
     * const count = await prisma.atsAnalysis.count({
     *   where: {
     *     // ... the filter for the AtsAnalyses we want to count
     *   }
     * })
    **/
    count<T extends AtsAnalysisCountArgs>(
      args?: Subset<T, AtsAnalysisCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AtsAnalysisCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a AtsAnalysis.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AtsAnalysisAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AtsAnalysisAggregateArgs>(args: Subset<T, AtsAnalysisAggregateArgs>): Prisma.PrismaPromise<GetAtsAnalysisAggregateType<T>>

    /**
     * Group by AtsAnalysis.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AtsAnalysisGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AtsAnalysisGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AtsAnalysisGroupByArgs['orderBy'] }
        : { orderBy?: AtsAnalysisGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AtsAnalysisGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAtsAnalysisGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the AtsAnalysis model
   */
  readonly fields: AtsAnalysisFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for AtsAnalysis.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AtsAnalysisClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the AtsAnalysis model
   */
  interface AtsAnalysisFieldRefs {
    readonly id: FieldRef<"AtsAnalysis", 'String'>
    readonly resumeId: FieldRef<"AtsAnalysis", 'String'>
    readonly overallScore: FieldRef<"AtsAnalysis", 'Float'>
    readonly keywordScore: FieldRef<"AtsAnalysis", 'Float'>
    readonly formatScore: FieldRef<"AtsAnalysis", 'Float'>
    readonly contentScore: FieldRef<"AtsAnalysis", 'Float'>
    readonly readabilityScore: FieldRef<"AtsAnalysis", 'Float'>
    readonly detectedIssues: FieldRef<"AtsAnalysis", 'Json'>
    readonly suggestedKeywords: FieldRef<"AtsAnalysis", 'Json'>
    readonly analysisDetails: FieldRef<"AtsAnalysis", 'Json'>
    readonly createdAt: FieldRef<"AtsAnalysis", 'DateTime'>
    readonly updatedAt: FieldRef<"AtsAnalysis", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * AtsAnalysis findUnique
   */
  export type AtsAnalysisFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * Filter, which AtsAnalysis to fetch.
     */
    where: AtsAnalysisWhereUniqueInput
  }

  /**
   * AtsAnalysis findUniqueOrThrow
   */
  export type AtsAnalysisFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * Filter, which AtsAnalysis to fetch.
     */
    where: AtsAnalysisWhereUniqueInput
  }

  /**
   * AtsAnalysis findFirst
   */
  export type AtsAnalysisFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * Filter, which AtsAnalysis to fetch.
     */
    where?: AtsAnalysisWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AtsAnalyses to fetch.
     */
    orderBy?: AtsAnalysisOrderByWithRelationInput | AtsAnalysisOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AtsAnalyses.
     */
    cursor?: AtsAnalysisWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AtsAnalyses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AtsAnalyses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AtsAnalyses.
     */
    distinct?: AtsAnalysisScalarFieldEnum | AtsAnalysisScalarFieldEnum[]
  }

  /**
   * AtsAnalysis findFirstOrThrow
   */
  export type AtsAnalysisFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * Filter, which AtsAnalysis to fetch.
     */
    where?: AtsAnalysisWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AtsAnalyses to fetch.
     */
    orderBy?: AtsAnalysisOrderByWithRelationInput | AtsAnalysisOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AtsAnalyses.
     */
    cursor?: AtsAnalysisWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AtsAnalyses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AtsAnalyses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AtsAnalyses.
     */
    distinct?: AtsAnalysisScalarFieldEnum | AtsAnalysisScalarFieldEnum[]
  }

  /**
   * AtsAnalysis findMany
   */
  export type AtsAnalysisFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * Filter, which AtsAnalyses to fetch.
     */
    where?: AtsAnalysisWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AtsAnalyses to fetch.
     */
    orderBy?: AtsAnalysisOrderByWithRelationInput | AtsAnalysisOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing AtsAnalyses.
     */
    cursor?: AtsAnalysisWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AtsAnalyses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AtsAnalyses.
     */
    skip?: number
    distinct?: AtsAnalysisScalarFieldEnum | AtsAnalysisScalarFieldEnum[]
  }

  /**
   * AtsAnalysis create
   */
  export type AtsAnalysisCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * The data needed to create a AtsAnalysis.
     */
    data: XOR<AtsAnalysisCreateInput, AtsAnalysisUncheckedCreateInput>
  }

  /**
   * AtsAnalysis createMany
   */
  export type AtsAnalysisCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many AtsAnalyses.
     */
    data: AtsAnalysisCreateManyInput | AtsAnalysisCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * AtsAnalysis createManyAndReturn
   */
  export type AtsAnalysisCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * The data used to create many AtsAnalyses.
     */
    data: AtsAnalysisCreateManyInput | AtsAnalysisCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * AtsAnalysis update
   */
  export type AtsAnalysisUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * The data needed to update a AtsAnalysis.
     */
    data: XOR<AtsAnalysisUpdateInput, AtsAnalysisUncheckedUpdateInput>
    /**
     * Choose, which AtsAnalysis to update.
     */
    where: AtsAnalysisWhereUniqueInput
  }

  /**
   * AtsAnalysis updateMany
   */
  export type AtsAnalysisUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update AtsAnalyses.
     */
    data: XOR<AtsAnalysisUpdateManyMutationInput, AtsAnalysisUncheckedUpdateManyInput>
    /**
     * Filter which AtsAnalyses to update
     */
    where?: AtsAnalysisWhereInput
    /**
     * Limit how many AtsAnalyses to update.
     */
    limit?: number
  }

  /**
   * AtsAnalysis updateManyAndReturn
   */
  export type AtsAnalysisUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * The data used to update AtsAnalyses.
     */
    data: XOR<AtsAnalysisUpdateManyMutationInput, AtsAnalysisUncheckedUpdateManyInput>
    /**
     * Filter which AtsAnalyses to update
     */
    where?: AtsAnalysisWhereInput
    /**
     * Limit how many AtsAnalyses to update.
     */
    limit?: number
  }

  /**
   * AtsAnalysis upsert
   */
  export type AtsAnalysisUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * The filter to search for the AtsAnalysis to update in case it exists.
     */
    where: AtsAnalysisWhereUniqueInput
    /**
     * In case the AtsAnalysis found by the `where` argument doesn't exist, create a new AtsAnalysis with this data.
     */
    create: XOR<AtsAnalysisCreateInput, AtsAnalysisUncheckedCreateInput>
    /**
     * In case the AtsAnalysis was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AtsAnalysisUpdateInput, AtsAnalysisUncheckedUpdateInput>
  }

  /**
   * AtsAnalysis delete
   */
  export type AtsAnalysisDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
    /**
     * Filter which AtsAnalysis to delete.
     */
    where: AtsAnalysisWhereUniqueInput
  }

  /**
   * AtsAnalysis deleteMany
   */
  export type AtsAnalysisDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AtsAnalyses to delete
     */
    where?: AtsAnalysisWhereInput
    /**
     * Limit how many AtsAnalyses to delete.
     */
    limit?: number
  }

  /**
   * AtsAnalysis without action
   */
  export type AtsAnalysisDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AtsAnalysis
     */
    select?: AtsAnalysisSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AtsAnalysis
     */
    omit?: AtsAnalysisOmit<ExtArgs> | null
  }


  /**
   * Model ParsedResume
   */

  export type AggregateParsedResume = {
    _count: ParsedResumeCountAggregateOutputType | null
    _avg: ParsedResumeAvgAggregateOutputType | null
    _sum: ParsedResumeSumAggregateOutputType | null
    _min: ParsedResumeMinAggregateOutputType | null
    _max: ParsedResumeMaxAggregateOutputType | null
  }

  export type ParsedResumeAvgAggregateOutputType = {
    parseTime: number | null
    overallScore: number | null
  }

  export type ParsedResumeSumAggregateOutputType = {
    parseTime: number | null
    overallScore: number | null
  }

  export type ParsedResumeMinAggregateOutputType = {
    id: string | null
    resumeId: string | null
    userId: string | null
    profileId: string | null
    parsedAt: Date | null
    parserVersion: string | null
    parserType: string | null
    fileType: string | null
    parseTime: number | null
    status: string | null
    name: string | null
    email: string | null
    phone: string | null
    location: string | null
    summary: string | null
    website: string | null
    rawText: string | null
    overallScore: number | null
  }

  export type ParsedResumeMaxAggregateOutputType = {
    id: string | null
    resumeId: string | null
    userId: string | null
    profileId: string | null
    parsedAt: Date | null
    parserVersion: string | null
    parserType: string | null
    fileType: string | null
    parseTime: number | null
    status: string | null
    name: string | null
    email: string | null
    phone: string | null
    location: string | null
    summary: string | null
    website: string | null
    rawText: string | null
    overallScore: number | null
  }

  export type ParsedResumeCountAggregateOutputType = {
    id: number
    resumeId: number
    userId: number
    profileId: number
    parsedAt: number
    parserVersion: number
    parserType: number
    fileType: number
    parseTime: number
    status: number
    name: number
    email: number
    phone: number
    location: number
    summary: number
    website: number
    education: number
    experience: number
    skills: number
    projects: number
    certifications: number
    languages: number
    publications: number
    achievements: number
    volunteer: number
    interests: number
    references: number
    patents: number
    rawText: number
    sectionMap: number
    confidenceScores: number
    overallScore: number
    _all: number
  }


  export type ParsedResumeAvgAggregateInputType = {
    parseTime?: true
    overallScore?: true
  }

  export type ParsedResumeSumAggregateInputType = {
    parseTime?: true
    overallScore?: true
  }

  export type ParsedResumeMinAggregateInputType = {
    id?: true
    resumeId?: true
    userId?: true
    profileId?: true
    parsedAt?: true
    parserVersion?: true
    parserType?: true
    fileType?: true
    parseTime?: true
    status?: true
    name?: true
    email?: true
    phone?: true
    location?: true
    summary?: true
    website?: true
    rawText?: true
    overallScore?: true
  }

  export type ParsedResumeMaxAggregateInputType = {
    id?: true
    resumeId?: true
    userId?: true
    profileId?: true
    parsedAt?: true
    parserVersion?: true
    parserType?: true
    fileType?: true
    parseTime?: true
    status?: true
    name?: true
    email?: true
    phone?: true
    location?: true
    summary?: true
    website?: true
    rawText?: true
    overallScore?: true
  }

  export type ParsedResumeCountAggregateInputType = {
    id?: true
    resumeId?: true
    userId?: true
    profileId?: true
    parsedAt?: true
    parserVersion?: true
    parserType?: true
    fileType?: true
    parseTime?: true
    status?: true
    name?: true
    email?: true
    phone?: true
    location?: true
    summary?: true
    website?: true
    education?: true
    experience?: true
    skills?: true
    projects?: true
    certifications?: true
    languages?: true
    publications?: true
    achievements?: true
    volunteer?: true
    interests?: true
    references?: true
    patents?: true
    rawText?: true
    sectionMap?: true
    confidenceScores?: true
    overallScore?: true
    _all?: true
  }

  export type ParsedResumeAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ParsedResume to aggregate.
     */
    where?: ParsedResumeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ParsedResumes to fetch.
     */
    orderBy?: ParsedResumeOrderByWithRelationInput | ParsedResumeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ParsedResumeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ParsedResumes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ParsedResumes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ParsedResumes
    **/
    _count?: true | ParsedResumeCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ParsedResumeAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ParsedResumeSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ParsedResumeMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ParsedResumeMaxAggregateInputType
  }

  export type GetParsedResumeAggregateType<T extends ParsedResumeAggregateArgs> = {
        [P in keyof T & keyof AggregateParsedResume]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateParsedResume[P]>
      : GetScalarType<T[P], AggregateParsedResume[P]>
  }




  export type ParsedResumeGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ParsedResumeWhereInput
    orderBy?: ParsedResumeOrderByWithAggregationInput | ParsedResumeOrderByWithAggregationInput[]
    by: ParsedResumeScalarFieldEnum[] | ParsedResumeScalarFieldEnum
    having?: ParsedResumeScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ParsedResumeCountAggregateInputType | true
    _avg?: ParsedResumeAvgAggregateInputType
    _sum?: ParsedResumeSumAggregateInputType
    _min?: ParsedResumeMinAggregateInputType
    _max?: ParsedResumeMaxAggregateInputType
  }

  export type ParsedResumeGroupByOutputType = {
    id: string
    resumeId: string
    userId: string
    profileId: string | null
    parsedAt: Date
    parserVersion: string
    parserType: string
    fileType: string | null
    parseTime: number | null
    status: string
    name: string | null
    email: string | null
    phone: string | null
    location: string | null
    summary: string | null
    website: string | null
    education: JsonValue | null
    experience: JsonValue | null
    skills: JsonValue | null
    projects: JsonValue | null
    certifications: JsonValue | null
    languages: JsonValue | null
    publications: JsonValue | null
    achievements: JsonValue | null
    volunteer: JsonValue | null
    interests: JsonValue | null
    references: JsonValue | null
    patents: JsonValue | null
    rawText: string | null
    sectionMap: JsonValue | null
    confidenceScores: JsonValue | null
    overallScore: number | null
    _count: ParsedResumeCountAggregateOutputType | null
    _avg: ParsedResumeAvgAggregateOutputType | null
    _sum: ParsedResumeSumAggregateOutputType | null
    _min: ParsedResumeMinAggregateOutputType | null
    _max: ParsedResumeMaxAggregateOutputType | null
  }

  type GetParsedResumeGroupByPayload<T extends ParsedResumeGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ParsedResumeGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ParsedResumeGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ParsedResumeGroupByOutputType[P]>
            : GetScalarType<T[P], ParsedResumeGroupByOutputType[P]>
        }
      >
    >


  export type ParsedResumeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    resumeId?: boolean
    userId?: boolean
    profileId?: boolean
    parsedAt?: boolean
    parserVersion?: boolean
    parserType?: boolean
    fileType?: boolean
    parseTime?: boolean
    status?: boolean
    name?: boolean
    email?: boolean
    phone?: boolean
    location?: boolean
    summary?: boolean
    website?: boolean
    education?: boolean
    experience?: boolean
    skills?: boolean
    projects?: boolean
    certifications?: boolean
    languages?: boolean
    publications?: boolean
    achievements?: boolean
    volunteer?: boolean
    interests?: boolean
    references?: boolean
    patents?: boolean
    rawText?: boolean
    sectionMap?: boolean
    confidenceScores?: boolean
    overallScore?: boolean
  }, ExtArgs["result"]["parsedResume"]>

  export type ParsedResumeSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    resumeId?: boolean
    userId?: boolean
    profileId?: boolean
    parsedAt?: boolean
    parserVersion?: boolean
    parserType?: boolean
    fileType?: boolean
    parseTime?: boolean
    status?: boolean
    name?: boolean
    email?: boolean
    phone?: boolean
    location?: boolean
    summary?: boolean
    website?: boolean
    education?: boolean
    experience?: boolean
    skills?: boolean
    projects?: boolean
    certifications?: boolean
    languages?: boolean
    publications?: boolean
    achievements?: boolean
    volunteer?: boolean
    interests?: boolean
    references?: boolean
    patents?: boolean
    rawText?: boolean
    sectionMap?: boolean
    confidenceScores?: boolean
    overallScore?: boolean
  }, ExtArgs["result"]["parsedResume"]>

  export type ParsedResumeSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    resumeId?: boolean
    userId?: boolean
    profileId?: boolean
    parsedAt?: boolean
    parserVersion?: boolean
    parserType?: boolean
    fileType?: boolean
    parseTime?: boolean
    status?: boolean
    name?: boolean
    email?: boolean
    phone?: boolean
    location?: boolean
    summary?: boolean
    website?: boolean
    education?: boolean
    experience?: boolean
    skills?: boolean
    projects?: boolean
    certifications?: boolean
    languages?: boolean
    publications?: boolean
    achievements?: boolean
    volunteer?: boolean
    interests?: boolean
    references?: boolean
    patents?: boolean
    rawText?: boolean
    sectionMap?: boolean
    confidenceScores?: boolean
    overallScore?: boolean
  }, ExtArgs["result"]["parsedResume"]>

  export type ParsedResumeSelectScalar = {
    id?: boolean
    resumeId?: boolean
    userId?: boolean
    profileId?: boolean
    parsedAt?: boolean
    parserVersion?: boolean
    parserType?: boolean
    fileType?: boolean
    parseTime?: boolean
    status?: boolean
    name?: boolean
    email?: boolean
    phone?: boolean
    location?: boolean
    summary?: boolean
    website?: boolean
    education?: boolean
    experience?: boolean
    skills?: boolean
    projects?: boolean
    certifications?: boolean
    languages?: boolean
    publications?: boolean
    achievements?: boolean
    volunteer?: boolean
    interests?: boolean
    references?: boolean
    patents?: boolean
    rawText?: boolean
    sectionMap?: boolean
    confidenceScores?: boolean
    overallScore?: boolean
  }

  export type ParsedResumeOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "resumeId" | "userId" | "profileId" | "parsedAt" | "parserVersion" | "parserType" | "fileType" | "parseTime" | "status" | "name" | "email" | "phone" | "location" | "summary" | "website" | "education" | "experience" | "skills" | "projects" | "certifications" | "languages" | "publications" | "achievements" | "volunteer" | "interests" | "references" | "patents" | "rawText" | "sectionMap" | "confidenceScores" | "overallScore", ExtArgs["result"]["parsedResume"]>

  export type $ParsedResumePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ParsedResume"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      resumeId: string
      userId: string
      profileId: string | null
      parsedAt: Date
      parserVersion: string
      parserType: string
      fileType: string | null
      parseTime: number | null
      status: string
      name: string | null
      email: string | null
      phone: string | null
      location: string | null
      summary: string | null
      website: string | null
      education: Prisma.JsonValue | null
      experience: Prisma.JsonValue | null
      skills: Prisma.JsonValue | null
      projects: Prisma.JsonValue | null
      certifications: Prisma.JsonValue | null
      languages: Prisma.JsonValue | null
      publications: Prisma.JsonValue | null
      achievements: Prisma.JsonValue | null
      volunteer: Prisma.JsonValue | null
      interests: Prisma.JsonValue | null
      references: Prisma.JsonValue | null
      patents: Prisma.JsonValue | null
      rawText: string | null
      sectionMap: Prisma.JsonValue | null
      confidenceScores: Prisma.JsonValue | null
      overallScore: number | null
    }, ExtArgs["result"]["parsedResume"]>
    composites: {}
  }

  type ParsedResumeGetPayload<S extends boolean | null | undefined | ParsedResumeDefaultArgs> = $Result.GetResult<Prisma.$ParsedResumePayload, S>

  type ParsedResumeCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ParsedResumeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ParsedResumeCountAggregateInputType | true
    }

  export interface ParsedResumeDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ParsedResume'], meta: { name: 'ParsedResume' } }
    /**
     * Find zero or one ParsedResume that matches the filter.
     * @param {ParsedResumeFindUniqueArgs} args - Arguments to find a ParsedResume
     * @example
     * // Get one ParsedResume
     * const parsedResume = await prisma.parsedResume.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ParsedResumeFindUniqueArgs>(args: SelectSubset<T, ParsedResumeFindUniqueArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ParsedResume that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ParsedResumeFindUniqueOrThrowArgs} args - Arguments to find a ParsedResume
     * @example
     * // Get one ParsedResume
     * const parsedResume = await prisma.parsedResume.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ParsedResumeFindUniqueOrThrowArgs>(args: SelectSubset<T, ParsedResumeFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ParsedResume that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ParsedResumeFindFirstArgs} args - Arguments to find a ParsedResume
     * @example
     * // Get one ParsedResume
     * const parsedResume = await prisma.parsedResume.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ParsedResumeFindFirstArgs>(args?: SelectSubset<T, ParsedResumeFindFirstArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ParsedResume that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ParsedResumeFindFirstOrThrowArgs} args - Arguments to find a ParsedResume
     * @example
     * // Get one ParsedResume
     * const parsedResume = await prisma.parsedResume.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ParsedResumeFindFirstOrThrowArgs>(args?: SelectSubset<T, ParsedResumeFindFirstOrThrowArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ParsedResumes that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ParsedResumeFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ParsedResumes
     * const parsedResumes = await prisma.parsedResume.findMany()
     * 
     * // Get first 10 ParsedResumes
     * const parsedResumes = await prisma.parsedResume.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const parsedResumeWithIdOnly = await prisma.parsedResume.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ParsedResumeFindManyArgs>(args?: SelectSubset<T, ParsedResumeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ParsedResume.
     * @param {ParsedResumeCreateArgs} args - Arguments to create a ParsedResume.
     * @example
     * // Create one ParsedResume
     * const ParsedResume = await prisma.parsedResume.create({
     *   data: {
     *     // ... data to create a ParsedResume
     *   }
     * })
     * 
     */
    create<T extends ParsedResumeCreateArgs>(args: SelectSubset<T, ParsedResumeCreateArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ParsedResumes.
     * @param {ParsedResumeCreateManyArgs} args - Arguments to create many ParsedResumes.
     * @example
     * // Create many ParsedResumes
     * const parsedResume = await prisma.parsedResume.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ParsedResumeCreateManyArgs>(args?: SelectSubset<T, ParsedResumeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ParsedResumes and returns the data saved in the database.
     * @param {ParsedResumeCreateManyAndReturnArgs} args - Arguments to create many ParsedResumes.
     * @example
     * // Create many ParsedResumes
     * const parsedResume = await prisma.parsedResume.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ParsedResumes and only return the `id`
     * const parsedResumeWithIdOnly = await prisma.parsedResume.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ParsedResumeCreateManyAndReturnArgs>(args?: SelectSubset<T, ParsedResumeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a ParsedResume.
     * @param {ParsedResumeDeleteArgs} args - Arguments to delete one ParsedResume.
     * @example
     * // Delete one ParsedResume
     * const ParsedResume = await prisma.parsedResume.delete({
     *   where: {
     *     // ... filter to delete one ParsedResume
     *   }
     * })
     * 
     */
    delete<T extends ParsedResumeDeleteArgs>(args: SelectSubset<T, ParsedResumeDeleteArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ParsedResume.
     * @param {ParsedResumeUpdateArgs} args - Arguments to update one ParsedResume.
     * @example
     * // Update one ParsedResume
     * const parsedResume = await prisma.parsedResume.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ParsedResumeUpdateArgs>(args: SelectSubset<T, ParsedResumeUpdateArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ParsedResumes.
     * @param {ParsedResumeDeleteManyArgs} args - Arguments to filter ParsedResumes to delete.
     * @example
     * // Delete a few ParsedResumes
     * const { count } = await prisma.parsedResume.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ParsedResumeDeleteManyArgs>(args?: SelectSubset<T, ParsedResumeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ParsedResumes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ParsedResumeUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ParsedResumes
     * const parsedResume = await prisma.parsedResume.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ParsedResumeUpdateManyArgs>(args: SelectSubset<T, ParsedResumeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ParsedResumes and returns the data updated in the database.
     * @param {ParsedResumeUpdateManyAndReturnArgs} args - Arguments to update many ParsedResumes.
     * @example
     * // Update many ParsedResumes
     * const parsedResume = await prisma.parsedResume.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more ParsedResumes and only return the `id`
     * const parsedResumeWithIdOnly = await prisma.parsedResume.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ParsedResumeUpdateManyAndReturnArgs>(args: SelectSubset<T, ParsedResumeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one ParsedResume.
     * @param {ParsedResumeUpsertArgs} args - Arguments to update or create a ParsedResume.
     * @example
     * // Update or create a ParsedResume
     * const parsedResume = await prisma.parsedResume.upsert({
     *   create: {
     *     // ... data to create a ParsedResume
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ParsedResume we want to update
     *   }
     * })
     */
    upsert<T extends ParsedResumeUpsertArgs>(args: SelectSubset<T, ParsedResumeUpsertArgs<ExtArgs>>): Prisma__ParsedResumeClient<$Result.GetResult<Prisma.$ParsedResumePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of ParsedResumes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ParsedResumeCountArgs} args - Arguments to filter ParsedResumes to count.
     * @example
     * // Count the number of ParsedResumes
     * const count = await prisma.parsedResume.count({
     *   where: {
     *     // ... the filter for the ParsedResumes we want to count
     *   }
     * })
    **/
    count<T extends ParsedResumeCountArgs>(
      args?: Subset<T, ParsedResumeCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ParsedResumeCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ParsedResume.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ParsedResumeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ParsedResumeAggregateArgs>(args: Subset<T, ParsedResumeAggregateArgs>): Prisma.PrismaPromise<GetParsedResumeAggregateType<T>>

    /**
     * Group by ParsedResume.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ParsedResumeGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ParsedResumeGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ParsedResumeGroupByArgs['orderBy'] }
        : { orderBy?: ParsedResumeGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ParsedResumeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetParsedResumeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ParsedResume model
   */
  readonly fields: ParsedResumeFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ParsedResume.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ParsedResumeClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ParsedResume model
   */
  interface ParsedResumeFieldRefs {
    readonly id: FieldRef<"ParsedResume", 'String'>
    readonly resumeId: FieldRef<"ParsedResume", 'String'>
    readonly userId: FieldRef<"ParsedResume", 'String'>
    readonly profileId: FieldRef<"ParsedResume", 'String'>
    readonly parsedAt: FieldRef<"ParsedResume", 'DateTime'>
    readonly parserVersion: FieldRef<"ParsedResume", 'String'>
    readonly parserType: FieldRef<"ParsedResume", 'String'>
    readonly fileType: FieldRef<"ParsedResume", 'String'>
    readonly parseTime: FieldRef<"ParsedResume", 'Int'>
    readonly status: FieldRef<"ParsedResume", 'String'>
    readonly name: FieldRef<"ParsedResume", 'String'>
    readonly email: FieldRef<"ParsedResume", 'String'>
    readonly phone: FieldRef<"ParsedResume", 'String'>
    readonly location: FieldRef<"ParsedResume", 'String'>
    readonly summary: FieldRef<"ParsedResume", 'String'>
    readonly website: FieldRef<"ParsedResume", 'String'>
    readonly education: FieldRef<"ParsedResume", 'Json'>
    readonly experience: FieldRef<"ParsedResume", 'Json'>
    readonly skills: FieldRef<"ParsedResume", 'Json'>
    readonly projects: FieldRef<"ParsedResume", 'Json'>
    readonly certifications: FieldRef<"ParsedResume", 'Json'>
    readonly languages: FieldRef<"ParsedResume", 'Json'>
    readonly publications: FieldRef<"ParsedResume", 'Json'>
    readonly achievements: FieldRef<"ParsedResume", 'Json'>
    readonly volunteer: FieldRef<"ParsedResume", 'Json'>
    readonly interests: FieldRef<"ParsedResume", 'Json'>
    readonly references: FieldRef<"ParsedResume", 'Json'>
    readonly patents: FieldRef<"ParsedResume", 'Json'>
    readonly rawText: FieldRef<"ParsedResume", 'String'>
    readonly sectionMap: FieldRef<"ParsedResume", 'Json'>
    readonly confidenceScores: FieldRef<"ParsedResume", 'Json'>
    readonly overallScore: FieldRef<"ParsedResume", 'Float'>
  }
    

  // Custom InputTypes
  /**
   * ParsedResume findUnique
   */
  export type ParsedResumeFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * Filter, which ParsedResume to fetch.
     */
    where: ParsedResumeWhereUniqueInput
  }

  /**
   * ParsedResume findUniqueOrThrow
   */
  export type ParsedResumeFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * Filter, which ParsedResume to fetch.
     */
    where: ParsedResumeWhereUniqueInput
  }

  /**
   * ParsedResume findFirst
   */
  export type ParsedResumeFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * Filter, which ParsedResume to fetch.
     */
    where?: ParsedResumeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ParsedResumes to fetch.
     */
    orderBy?: ParsedResumeOrderByWithRelationInput | ParsedResumeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ParsedResumes.
     */
    cursor?: ParsedResumeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ParsedResumes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ParsedResumes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ParsedResumes.
     */
    distinct?: ParsedResumeScalarFieldEnum | ParsedResumeScalarFieldEnum[]
  }

  /**
   * ParsedResume findFirstOrThrow
   */
  export type ParsedResumeFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * Filter, which ParsedResume to fetch.
     */
    where?: ParsedResumeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ParsedResumes to fetch.
     */
    orderBy?: ParsedResumeOrderByWithRelationInput | ParsedResumeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ParsedResumes.
     */
    cursor?: ParsedResumeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ParsedResumes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ParsedResumes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ParsedResumes.
     */
    distinct?: ParsedResumeScalarFieldEnum | ParsedResumeScalarFieldEnum[]
  }

  /**
   * ParsedResume findMany
   */
  export type ParsedResumeFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * Filter, which ParsedResumes to fetch.
     */
    where?: ParsedResumeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ParsedResumes to fetch.
     */
    orderBy?: ParsedResumeOrderByWithRelationInput | ParsedResumeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ParsedResumes.
     */
    cursor?: ParsedResumeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ParsedResumes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ParsedResumes.
     */
    skip?: number
    distinct?: ParsedResumeScalarFieldEnum | ParsedResumeScalarFieldEnum[]
  }

  /**
   * ParsedResume create
   */
  export type ParsedResumeCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * The data needed to create a ParsedResume.
     */
    data: XOR<ParsedResumeCreateInput, ParsedResumeUncheckedCreateInput>
  }

  /**
   * ParsedResume createMany
   */
  export type ParsedResumeCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ParsedResumes.
     */
    data: ParsedResumeCreateManyInput | ParsedResumeCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * ParsedResume createManyAndReturn
   */
  export type ParsedResumeCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * The data used to create many ParsedResumes.
     */
    data: ParsedResumeCreateManyInput | ParsedResumeCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * ParsedResume update
   */
  export type ParsedResumeUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * The data needed to update a ParsedResume.
     */
    data: XOR<ParsedResumeUpdateInput, ParsedResumeUncheckedUpdateInput>
    /**
     * Choose, which ParsedResume to update.
     */
    where: ParsedResumeWhereUniqueInput
  }

  /**
   * ParsedResume updateMany
   */
  export type ParsedResumeUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ParsedResumes.
     */
    data: XOR<ParsedResumeUpdateManyMutationInput, ParsedResumeUncheckedUpdateManyInput>
    /**
     * Filter which ParsedResumes to update
     */
    where?: ParsedResumeWhereInput
    /**
     * Limit how many ParsedResumes to update.
     */
    limit?: number
  }

  /**
   * ParsedResume updateManyAndReturn
   */
  export type ParsedResumeUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * The data used to update ParsedResumes.
     */
    data: XOR<ParsedResumeUpdateManyMutationInput, ParsedResumeUncheckedUpdateManyInput>
    /**
     * Filter which ParsedResumes to update
     */
    where?: ParsedResumeWhereInput
    /**
     * Limit how many ParsedResumes to update.
     */
    limit?: number
  }

  /**
   * ParsedResume upsert
   */
  export type ParsedResumeUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * The filter to search for the ParsedResume to update in case it exists.
     */
    where: ParsedResumeWhereUniqueInput
    /**
     * In case the ParsedResume found by the `where` argument doesn't exist, create a new ParsedResume with this data.
     */
    create: XOR<ParsedResumeCreateInput, ParsedResumeUncheckedCreateInput>
    /**
     * In case the ParsedResume was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ParsedResumeUpdateInput, ParsedResumeUncheckedUpdateInput>
  }

  /**
   * ParsedResume delete
   */
  export type ParsedResumeDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
    /**
     * Filter which ParsedResume to delete.
     */
    where: ParsedResumeWhereUniqueInput
  }

  /**
   * ParsedResume deleteMany
   */
  export type ParsedResumeDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ParsedResumes to delete
     */
    where?: ParsedResumeWhereInput
    /**
     * Limit how many ParsedResumes to delete.
     */
    limit?: number
  }

  /**
   * ParsedResume without action
   */
  export type ParsedResumeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ParsedResume
     */
    select?: ParsedResumeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ParsedResume
     */
    omit?: ParsedResumeOmit<ExtArgs> | null
  }


  /**
   * Model ServiceStatus
   */

  export type AggregateServiceStatus = {
    _count: ServiceStatusCountAggregateOutputType | null
    _min: ServiceStatusMinAggregateOutputType | null
    _max: ServiceStatusMaxAggregateOutputType | null
  }

  export type ServiceStatusMinAggregateOutputType = {
    id: string | null
    name: string | null
    status: string | null
    description: string | null
    lastCheckedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ServiceStatusMaxAggregateOutputType = {
    id: string | null
    name: string | null
    status: string | null
    description: string | null
    lastCheckedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ServiceStatusCountAggregateOutputType = {
    id: number
    name: number
    status: number
    description: number
    lastCheckedAt: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type ServiceStatusMinAggregateInputType = {
    id?: true
    name?: true
    status?: true
    description?: true
    lastCheckedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ServiceStatusMaxAggregateInputType = {
    id?: true
    name?: true
    status?: true
    description?: true
    lastCheckedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ServiceStatusCountAggregateInputType = {
    id?: true
    name?: true
    status?: true
    description?: true
    lastCheckedAt?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type ServiceStatusAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ServiceStatus to aggregate.
     */
    where?: ServiceStatusWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatuses to fetch.
     */
    orderBy?: ServiceStatusOrderByWithRelationInput | ServiceStatusOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ServiceStatusWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatuses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatuses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ServiceStatuses
    **/
    _count?: true | ServiceStatusCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ServiceStatusMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ServiceStatusMaxAggregateInputType
  }

  export type GetServiceStatusAggregateType<T extends ServiceStatusAggregateArgs> = {
        [P in keyof T & keyof AggregateServiceStatus]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateServiceStatus[P]>
      : GetScalarType<T[P], AggregateServiceStatus[P]>
  }




  export type ServiceStatusGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ServiceStatusWhereInput
    orderBy?: ServiceStatusOrderByWithAggregationInput | ServiceStatusOrderByWithAggregationInput[]
    by: ServiceStatusScalarFieldEnum[] | ServiceStatusScalarFieldEnum
    having?: ServiceStatusScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ServiceStatusCountAggregateInputType | true
    _min?: ServiceStatusMinAggregateInputType
    _max?: ServiceStatusMaxAggregateInputType
  }

  export type ServiceStatusGroupByOutputType = {
    id: string
    name: string
    status: string
    description: string | null
    lastCheckedAt: Date
    createdAt: Date
    updatedAt: Date
    _count: ServiceStatusCountAggregateOutputType | null
    _min: ServiceStatusMinAggregateOutputType | null
    _max: ServiceStatusMaxAggregateOutputType | null
  }

  type GetServiceStatusGroupByPayload<T extends ServiceStatusGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ServiceStatusGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ServiceStatusGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ServiceStatusGroupByOutputType[P]>
            : GetScalarType<T[P], ServiceStatusGroupByOutputType[P]>
        }
      >
    >


  export type ServiceStatusSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    status?: boolean
    description?: boolean
    lastCheckedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    statusHistory?: boolean | ServiceStatus$statusHistoryArgs<ExtArgs>
    _count?: boolean | ServiceStatusCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["serviceStatus"]>

  export type ServiceStatusSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    status?: boolean
    description?: boolean
    lastCheckedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["serviceStatus"]>

  export type ServiceStatusSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    status?: boolean
    description?: boolean
    lastCheckedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["serviceStatus"]>

  export type ServiceStatusSelectScalar = {
    id?: boolean
    name?: boolean
    status?: boolean
    description?: boolean
    lastCheckedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type ServiceStatusOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "name" | "status" | "description" | "lastCheckedAt" | "createdAt" | "updatedAt", ExtArgs["result"]["serviceStatus"]>
  export type ServiceStatusInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    statusHistory?: boolean | ServiceStatus$statusHistoryArgs<ExtArgs>
    _count?: boolean | ServiceStatusCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ServiceStatusIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type ServiceStatusIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $ServiceStatusPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ServiceStatus"
    objects: {
      statusHistory: Prisma.$ServiceStatusHistoryPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string
      status: string
      description: string | null
      lastCheckedAt: Date
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["serviceStatus"]>
    composites: {}
  }

  type ServiceStatusGetPayload<S extends boolean | null | undefined | ServiceStatusDefaultArgs> = $Result.GetResult<Prisma.$ServiceStatusPayload, S>

  type ServiceStatusCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ServiceStatusFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ServiceStatusCountAggregateInputType | true
    }

  export interface ServiceStatusDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ServiceStatus'], meta: { name: 'ServiceStatus' } }
    /**
     * Find zero or one ServiceStatus that matches the filter.
     * @param {ServiceStatusFindUniqueArgs} args - Arguments to find a ServiceStatus
     * @example
     * // Get one ServiceStatus
     * const serviceStatus = await prisma.serviceStatus.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ServiceStatusFindUniqueArgs>(args: SelectSubset<T, ServiceStatusFindUniqueArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ServiceStatus that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ServiceStatusFindUniqueOrThrowArgs} args - Arguments to find a ServiceStatus
     * @example
     * // Get one ServiceStatus
     * const serviceStatus = await prisma.serviceStatus.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ServiceStatusFindUniqueOrThrowArgs>(args: SelectSubset<T, ServiceStatusFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ServiceStatus that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusFindFirstArgs} args - Arguments to find a ServiceStatus
     * @example
     * // Get one ServiceStatus
     * const serviceStatus = await prisma.serviceStatus.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ServiceStatusFindFirstArgs>(args?: SelectSubset<T, ServiceStatusFindFirstArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ServiceStatus that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusFindFirstOrThrowArgs} args - Arguments to find a ServiceStatus
     * @example
     * // Get one ServiceStatus
     * const serviceStatus = await prisma.serviceStatus.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ServiceStatusFindFirstOrThrowArgs>(args?: SelectSubset<T, ServiceStatusFindFirstOrThrowArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ServiceStatuses that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ServiceStatuses
     * const serviceStatuses = await prisma.serviceStatus.findMany()
     * 
     * // Get first 10 ServiceStatuses
     * const serviceStatuses = await prisma.serviceStatus.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const serviceStatusWithIdOnly = await prisma.serviceStatus.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ServiceStatusFindManyArgs>(args?: SelectSubset<T, ServiceStatusFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ServiceStatus.
     * @param {ServiceStatusCreateArgs} args - Arguments to create a ServiceStatus.
     * @example
     * // Create one ServiceStatus
     * const ServiceStatus = await prisma.serviceStatus.create({
     *   data: {
     *     // ... data to create a ServiceStatus
     *   }
     * })
     * 
     */
    create<T extends ServiceStatusCreateArgs>(args: SelectSubset<T, ServiceStatusCreateArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ServiceStatuses.
     * @param {ServiceStatusCreateManyArgs} args - Arguments to create many ServiceStatuses.
     * @example
     * // Create many ServiceStatuses
     * const serviceStatus = await prisma.serviceStatus.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ServiceStatusCreateManyArgs>(args?: SelectSubset<T, ServiceStatusCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ServiceStatuses and returns the data saved in the database.
     * @param {ServiceStatusCreateManyAndReturnArgs} args - Arguments to create many ServiceStatuses.
     * @example
     * // Create many ServiceStatuses
     * const serviceStatus = await prisma.serviceStatus.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ServiceStatuses and only return the `id`
     * const serviceStatusWithIdOnly = await prisma.serviceStatus.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ServiceStatusCreateManyAndReturnArgs>(args?: SelectSubset<T, ServiceStatusCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a ServiceStatus.
     * @param {ServiceStatusDeleteArgs} args - Arguments to delete one ServiceStatus.
     * @example
     * // Delete one ServiceStatus
     * const ServiceStatus = await prisma.serviceStatus.delete({
     *   where: {
     *     // ... filter to delete one ServiceStatus
     *   }
     * })
     * 
     */
    delete<T extends ServiceStatusDeleteArgs>(args: SelectSubset<T, ServiceStatusDeleteArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ServiceStatus.
     * @param {ServiceStatusUpdateArgs} args - Arguments to update one ServiceStatus.
     * @example
     * // Update one ServiceStatus
     * const serviceStatus = await prisma.serviceStatus.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ServiceStatusUpdateArgs>(args: SelectSubset<T, ServiceStatusUpdateArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ServiceStatuses.
     * @param {ServiceStatusDeleteManyArgs} args - Arguments to filter ServiceStatuses to delete.
     * @example
     * // Delete a few ServiceStatuses
     * const { count } = await prisma.serviceStatus.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ServiceStatusDeleteManyArgs>(args?: SelectSubset<T, ServiceStatusDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ServiceStatuses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ServiceStatuses
     * const serviceStatus = await prisma.serviceStatus.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ServiceStatusUpdateManyArgs>(args: SelectSubset<T, ServiceStatusUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ServiceStatuses and returns the data updated in the database.
     * @param {ServiceStatusUpdateManyAndReturnArgs} args - Arguments to update many ServiceStatuses.
     * @example
     * // Update many ServiceStatuses
     * const serviceStatus = await prisma.serviceStatus.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more ServiceStatuses and only return the `id`
     * const serviceStatusWithIdOnly = await prisma.serviceStatus.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ServiceStatusUpdateManyAndReturnArgs>(args: SelectSubset<T, ServiceStatusUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one ServiceStatus.
     * @param {ServiceStatusUpsertArgs} args - Arguments to update or create a ServiceStatus.
     * @example
     * // Update or create a ServiceStatus
     * const serviceStatus = await prisma.serviceStatus.upsert({
     *   create: {
     *     // ... data to create a ServiceStatus
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ServiceStatus we want to update
     *   }
     * })
     */
    upsert<T extends ServiceStatusUpsertArgs>(args: SelectSubset<T, ServiceStatusUpsertArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of ServiceStatuses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusCountArgs} args - Arguments to filter ServiceStatuses to count.
     * @example
     * // Count the number of ServiceStatuses
     * const count = await prisma.serviceStatus.count({
     *   where: {
     *     // ... the filter for the ServiceStatuses we want to count
     *   }
     * })
    **/
    count<T extends ServiceStatusCountArgs>(
      args?: Subset<T, ServiceStatusCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ServiceStatusCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ServiceStatus.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ServiceStatusAggregateArgs>(args: Subset<T, ServiceStatusAggregateArgs>): Prisma.PrismaPromise<GetServiceStatusAggregateType<T>>

    /**
     * Group by ServiceStatus.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ServiceStatusGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ServiceStatusGroupByArgs['orderBy'] }
        : { orderBy?: ServiceStatusGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ServiceStatusGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetServiceStatusGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ServiceStatus model
   */
  readonly fields: ServiceStatusFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ServiceStatus.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ServiceStatusClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    statusHistory<T extends ServiceStatus$statusHistoryArgs<ExtArgs> = {}>(args?: Subset<T, ServiceStatus$statusHistoryArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ServiceStatus model
   */
  interface ServiceStatusFieldRefs {
    readonly id: FieldRef<"ServiceStatus", 'String'>
    readonly name: FieldRef<"ServiceStatus", 'String'>
    readonly status: FieldRef<"ServiceStatus", 'String'>
    readonly description: FieldRef<"ServiceStatus", 'String'>
    readonly lastCheckedAt: FieldRef<"ServiceStatus", 'DateTime'>
    readonly createdAt: FieldRef<"ServiceStatus", 'DateTime'>
    readonly updatedAt: FieldRef<"ServiceStatus", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * ServiceStatus findUnique
   */
  export type ServiceStatusFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatus to fetch.
     */
    where: ServiceStatusWhereUniqueInput
  }

  /**
   * ServiceStatus findUniqueOrThrow
   */
  export type ServiceStatusFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatus to fetch.
     */
    where: ServiceStatusWhereUniqueInput
  }

  /**
   * ServiceStatus findFirst
   */
  export type ServiceStatusFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatus to fetch.
     */
    where?: ServiceStatusWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatuses to fetch.
     */
    orderBy?: ServiceStatusOrderByWithRelationInput | ServiceStatusOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ServiceStatuses.
     */
    cursor?: ServiceStatusWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatuses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatuses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ServiceStatuses.
     */
    distinct?: ServiceStatusScalarFieldEnum | ServiceStatusScalarFieldEnum[]
  }

  /**
   * ServiceStatus findFirstOrThrow
   */
  export type ServiceStatusFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatus to fetch.
     */
    where?: ServiceStatusWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatuses to fetch.
     */
    orderBy?: ServiceStatusOrderByWithRelationInput | ServiceStatusOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ServiceStatuses.
     */
    cursor?: ServiceStatusWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatuses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatuses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ServiceStatuses.
     */
    distinct?: ServiceStatusScalarFieldEnum | ServiceStatusScalarFieldEnum[]
  }

  /**
   * ServiceStatus findMany
   */
  export type ServiceStatusFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatuses to fetch.
     */
    where?: ServiceStatusWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatuses to fetch.
     */
    orderBy?: ServiceStatusOrderByWithRelationInput | ServiceStatusOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ServiceStatuses.
     */
    cursor?: ServiceStatusWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatuses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatuses.
     */
    skip?: number
    distinct?: ServiceStatusScalarFieldEnum | ServiceStatusScalarFieldEnum[]
  }

  /**
   * ServiceStatus create
   */
  export type ServiceStatusCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * The data needed to create a ServiceStatus.
     */
    data: XOR<ServiceStatusCreateInput, ServiceStatusUncheckedCreateInput>
  }

  /**
   * ServiceStatus createMany
   */
  export type ServiceStatusCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ServiceStatuses.
     */
    data: ServiceStatusCreateManyInput | ServiceStatusCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * ServiceStatus createManyAndReturn
   */
  export type ServiceStatusCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * The data used to create many ServiceStatuses.
     */
    data: ServiceStatusCreateManyInput | ServiceStatusCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * ServiceStatus update
   */
  export type ServiceStatusUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * The data needed to update a ServiceStatus.
     */
    data: XOR<ServiceStatusUpdateInput, ServiceStatusUncheckedUpdateInput>
    /**
     * Choose, which ServiceStatus to update.
     */
    where: ServiceStatusWhereUniqueInput
  }

  /**
   * ServiceStatus updateMany
   */
  export type ServiceStatusUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ServiceStatuses.
     */
    data: XOR<ServiceStatusUpdateManyMutationInput, ServiceStatusUncheckedUpdateManyInput>
    /**
     * Filter which ServiceStatuses to update
     */
    where?: ServiceStatusWhereInput
    /**
     * Limit how many ServiceStatuses to update.
     */
    limit?: number
  }

  /**
   * ServiceStatus updateManyAndReturn
   */
  export type ServiceStatusUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * The data used to update ServiceStatuses.
     */
    data: XOR<ServiceStatusUpdateManyMutationInput, ServiceStatusUncheckedUpdateManyInput>
    /**
     * Filter which ServiceStatuses to update
     */
    where?: ServiceStatusWhereInput
    /**
     * Limit how many ServiceStatuses to update.
     */
    limit?: number
  }

  /**
   * ServiceStatus upsert
   */
  export type ServiceStatusUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * The filter to search for the ServiceStatus to update in case it exists.
     */
    where: ServiceStatusWhereUniqueInput
    /**
     * In case the ServiceStatus found by the `where` argument doesn't exist, create a new ServiceStatus with this data.
     */
    create: XOR<ServiceStatusCreateInput, ServiceStatusUncheckedCreateInput>
    /**
     * In case the ServiceStatus was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ServiceStatusUpdateInput, ServiceStatusUncheckedUpdateInput>
  }

  /**
   * ServiceStatus delete
   */
  export type ServiceStatusDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
    /**
     * Filter which ServiceStatus to delete.
     */
    where: ServiceStatusWhereUniqueInput
  }

  /**
   * ServiceStatus deleteMany
   */
  export type ServiceStatusDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ServiceStatuses to delete
     */
    where?: ServiceStatusWhereInput
    /**
     * Limit how many ServiceStatuses to delete.
     */
    limit?: number
  }

  /**
   * ServiceStatus.statusHistory
   */
  export type ServiceStatus$statusHistoryArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    where?: ServiceStatusHistoryWhereInput
    orderBy?: ServiceStatusHistoryOrderByWithRelationInput | ServiceStatusHistoryOrderByWithRelationInput[]
    cursor?: ServiceStatusHistoryWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ServiceStatusHistoryScalarFieldEnum | ServiceStatusHistoryScalarFieldEnum[]
  }

  /**
   * ServiceStatus without action
   */
  export type ServiceStatusDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatus
     */
    select?: ServiceStatusSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatus
     */
    omit?: ServiceStatusOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusInclude<ExtArgs> | null
  }


  /**
   * Model ServiceStatusHistory
   */

  export type AggregateServiceStatusHistory = {
    _count: ServiceStatusHistoryCountAggregateOutputType | null
    _min: ServiceStatusHistoryMinAggregateOutputType | null
    _max: ServiceStatusHistoryMaxAggregateOutputType | null
  }

  export type ServiceStatusHistoryMinAggregateOutputType = {
    id: string | null
    serviceId: string | null
    status: string | null
    recordedAt: Date | null
    createdAt: Date | null
  }

  export type ServiceStatusHistoryMaxAggregateOutputType = {
    id: string | null
    serviceId: string | null
    status: string | null
    recordedAt: Date | null
    createdAt: Date | null
  }

  export type ServiceStatusHistoryCountAggregateOutputType = {
    id: number
    serviceId: number
    status: number
    recordedAt: number
    createdAt: number
    _all: number
  }


  export type ServiceStatusHistoryMinAggregateInputType = {
    id?: true
    serviceId?: true
    status?: true
    recordedAt?: true
    createdAt?: true
  }

  export type ServiceStatusHistoryMaxAggregateInputType = {
    id?: true
    serviceId?: true
    status?: true
    recordedAt?: true
    createdAt?: true
  }

  export type ServiceStatusHistoryCountAggregateInputType = {
    id?: true
    serviceId?: true
    status?: true
    recordedAt?: true
    createdAt?: true
    _all?: true
  }

  export type ServiceStatusHistoryAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ServiceStatusHistory to aggregate.
     */
    where?: ServiceStatusHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatusHistories to fetch.
     */
    orderBy?: ServiceStatusHistoryOrderByWithRelationInput | ServiceStatusHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ServiceStatusHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatusHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatusHistories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ServiceStatusHistories
    **/
    _count?: true | ServiceStatusHistoryCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ServiceStatusHistoryMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ServiceStatusHistoryMaxAggregateInputType
  }

  export type GetServiceStatusHistoryAggregateType<T extends ServiceStatusHistoryAggregateArgs> = {
        [P in keyof T & keyof AggregateServiceStatusHistory]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateServiceStatusHistory[P]>
      : GetScalarType<T[P], AggregateServiceStatusHistory[P]>
  }




  export type ServiceStatusHistoryGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ServiceStatusHistoryWhereInput
    orderBy?: ServiceStatusHistoryOrderByWithAggregationInput | ServiceStatusHistoryOrderByWithAggregationInput[]
    by: ServiceStatusHistoryScalarFieldEnum[] | ServiceStatusHistoryScalarFieldEnum
    having?: ServiceStatusHistoryScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ServiceStatusHistoryCountAggregateInputType | true
    _min?: ServiceStatusHistoryMinAggregateInputType
    _max?: ServiceStatusHistoryMaxAggregateInputType
  }

  export type ServiceStatusHistoryGroupByOutputType = {
    id: string
    serviceId: string
    status: string
    recordedAt: Date
    createdAt: Date
    _count: ServiceStatusHistoryCountAggregateOutputType | null
    _min: ServiceStatusHistoryMinAggregateOutputType | null
    _max: ServiceStatusHistoryMaxAggregateOutputType | null
  }

  type GetServiceStatusHistoryGroupByPayload<T extends ServiceStatusHistoryGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ServiceStatusHistoryGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ServiceStatusHistoryGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ServiceStatusHistoryGroupByOutputType[P]>
            : GetScalarType<T[P], ServiceStatusHistoryGroupByOutputType[P]>
        }
      >
    >


  export type ServiceStatusHistorySelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    serviceId?: boolean
    status?: boolean
    recordedAt?: boolean
    createdAt?: boolean
    service?: boolean | ServiceStatusDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["serviceStatusHistory"]>

  export type ServiceStatusHistorySelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    serviceId?: boolean
    status?: boolean
    recordedAt?: boolean
    createdAt?: boolean
    service?: boolean | ServiceStatusDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["serviceStatusHistory"]>

  export type ServiceStatusHistorySelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    serviceId?: boolean
    status?: boolean
    recordedAt?: boolean
    createdAt?: boolean
    service?: boolean | ServiceStatusDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["serviceStatusHistory"]>

  export type ServiceStatusHistorySelectScalar = {
    id?: boolean
    serviceId?: boolean
    status?: boolean
    recordedAt?: boolean
    createdAt?: boolean
  }

  export type ServiceStatusHistoryOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "serviceId" | "status" | "recordedAt" | "createdAt", ExtArgs["result"]["serviceStatusHistory"]>
  export type ServiceStatusHistoryInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    service?: boolean | ServiceStatusDefaultArgs<ExtArgs>
  }
  export type ServiceStatusHistoryIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    service?: boolean | ServiceStatusDefaultArgs<ExtArgs>
  }
  export type ServiceStatusHistoryIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    service?: boolean | ServiceStatusDefaultArgs<ExtArgs>
  }

  export type $ServiceStatusHistoryPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ServiceStatusHistory"
    objects: {
      service: Prisma.$ServiceStatusPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      serviceId: string
      status: string
      recordedAt: Date
      createdAt: Date
    }, ExtArgs["result"]["serviceStatusHistory"]>
    composites: {}
  }

  type ServiceStatusHistoryGetPayload<S extends boolean | null | undefined | ServiceStatusHistoryDefaultArgs> = $Result.GetResult<Prisma.$ServiceStatusHistoryPayload, S>

  type ServiceStatusHistoryCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ServiceStatusHistoryFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ServiceStatusHistoryCountAggregateInputType | true
    }

  export interface ServiceStatusHistoryDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ServiceStatusHistory'], meta: { name: 'ServiceStatusHistory' } }
    /**
     * Find zero or one ServiceStatusHistory that matches the filter.
     * @param {ServiceStatusHistoryFindUniqueArgs} args - Arguments to find a ServiceStatusHistory
     * @example
     * // Get one ServiceStatusHistory
     * const serviceStatusHistory = await prisma.serviceStatusHistory.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ServiceStatusHistoryFindUniqueArgs>(args: SelectSubset<T, ServiceStatusHistoryFindUniqueArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ServiceStatusHistory that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ServiceStatusHistoryFindUniqueOrThrowArgs} args - Arguments to find a ServiceStatusHistory
     * @example
     * // Get one ServiceStatusHistory
     * const serviceStatusHistory = await prisma.serviceStatusHistory.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ServiceStatusHistoryFindUniqueOrThrowArgs>(args: SelectSubset<T, ServiceStatusHistoryFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ServiceStatusHistory that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusHistoryFindFirstArgs} args - Arguments to find a ServiceStatusHistory
     * @example
     * // Get one ServiceStatusHistory
     * const serviceStatusHistory = await prisma.serviceStatusHistory.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ServiceStatusHistoryFindFirstArgs>(args?: SelectSubset<T, ServiceStatusHistoryFindFirstArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ServiceStatusHistory that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusHistoryFindFirstOrThrowArgs} args - Arguments to find a ServiceStatusHistory
     * @example
     * // Get one ServiceStatusHistory
     * const serviceStatusHistory = await prisma.serviceStatusHistory.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ServiceStatusHistoryFindFirstOrThrowArgs>(args?: SelectSubset<T, ServiceStatusHistoryFindFirstOrThrowArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ServiceStatusHistories that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusHistoryFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ServiceStatusHistories
     * const serviceStatusHistories = await prisma.serviceStatusHistory.findMany()
     * 
     * // Get first 10 ServiceStatusHistories
     * const serviceStatusHistories = await prisma.serviceStatusHistory.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const serviceStatusHistoryWithIdOnly = await prisma.serviceStatusHistory.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ServiceStatusHistoryFindManyArgs>(args?: SelectSubset<T, ServiceStatusHistoryFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ServiceStatusHistory.
     * @param {ServiceStatusHistoryCreateArgs} args - Arguments to create a ServiceStatusHistory.
     * @example
     * // Create one ServiceStatusHistory
     * const ServiceStatusHistory = await prisma.serviceStatusHistory.create({
     *   data: {
     *     // ... data to create a ServiceStatusHistory
     *   }
     * })
     * 
     */
    create<T extends ServiceStatusHistoryCreateArgs>(args: SelectSubset<T, ServiceStatusHistoryCreateArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ServiceStatusHistories.
     * @param {ServiceStatusHistoryCreateManyArgs} args - Arguments to create many ServiceStatusHistories.
     * @example
     * // Create many ServiceStatusHistories
     * const serviceStatusHistory = await prisma.serviceStatusHistory.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ServiceStatusHistoryCreateManyArgs>(args?: SelectSubset<T, ServiceStatusHistoryCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ServiceStatusHistories and returns the data saved in the database.
     * @param {ServiceStatusHistoryCreateManyAndReturnArgs} args - Arguments to create many ServiceStatusHistories.
     * @example
     * // Create many ServiceStatusHistories
     * const serviceStatusHistory = await prisma.serviceStatusHistory.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ServiceStatusHistories and only return the `id`
     * const serviceStatusHistoryWithIdOnly = await prisma.serviceStatusHistory.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ServiceStatusHistoryCreateManyAndReturnArgs>(args?: SelectSubset<T, ServiceStatusHistoryCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a ServiceStatusHistory.
     * @param {ServiceStatusHistoryDeleteArgs} args - Arguments to delete one ServiceStatusHistory.
     * @example
     * // Delete one ServiceStatusHistory
     * const ServiceStatusHistory = await prisma.serviceStatusHistory.delete({
     *   where: {
     *     // ... filter to delete one ServiceStatusHistory
     *   }
     * })
     * 
     */
    delete<T extends ServiceStatusHistoryDeleteArgs>(args: SelectSubset<T, ServiceStatusHistoryDeleteArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ServiceStatusHistory.
     * @param {ServiceStatusHistoryUpdateArgs} args - Arguments to update one ServiceStatusHistory.
     * @example
     * // Update one ServiceStatusHistory
     * const serviceStatusHistory = await prisma.serviceStatusHistory.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ServiceStatusHistoryUpdateArgs>(args: SelectSubset<T, ServiceStatusHistoryUpdateArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ServiceStatusHistories.
     * @param {ServiceStatusHistoryDeleteManyArgs} args - Arguments to filter ServiceStatusHistories to delete.
     * @example
     * // Delete a few ServiceStatusHistories
     * const { count } = await prisma.serviceStatusHistory.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ServiceStatusHistoryDeleteManyArgs>(args?: SelectSubset<T, ServiceStatusHistoryDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ServiceStatusHistories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusHistoryUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ServiceStatusHistories
     * const serviceStatusHistory = await prisma.serviceStatusHistory.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ServiceStatusHistoryUpdateManyArgs>(args: SelectSubset<T, ServiceStatusHistoryUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ServiceStatusHistories and returns the data updated in the database.
     * @param {ServiceStatusHistoryUpdateManyAndReturnArgs} args - Arguments to update many ServiceStatusHistories.
     * @example
     * // Update many ServiceStatusHistories
     * const serviceStatusHistory = await prisma.serviceStatusHistory.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more ServiceStatusHistories and only return the `id`
     * const serviceStatusHistoryWithIdOnly = await prisma.serviceStatusHistory.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ServiceStatusHistoryUpdateManyAndReturnArgs>(args: SelectSubset<T, ServiceStatusHistoryUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one ServiceStatusHistory.
     * @param {ServiceStatusHistoryUpsertArgs} args - Arguments to update or create a ServiceStatusHistory.
     * @example
     * // Update or create a ServiceStatusHistory
     * const serviceStatusHistory = await prisma.serviceStatusHistory.upsert({
     *   create: {
     *     // ... data to create a ServiceStatusHistory
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ServiceStatusHistory we want to update
     *   }
     * })
     */
    upsert<T extends ServiceStatusHistoryUpsertArgs>(args: SelectSubset<T, ServiceStatusHistoryUpsertArgs<ExtArgs>>): Prisma__ServiceStatusHistoryClient<$Result.GetResult<Prisma.$ServiceStatusHistoryPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of ServiceStatusHistories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusHistoryCountArgs} args - Arguments to filter ServiceStatusHistories to count.
     * @example
     * // Count the number of ServiceStatusHistories
     * const count = await prisma.serviceStatusHistory.count({
     *   where: {
     *     // ... the filter for the ServiceStatusHistories we want to count
     *   }
     * })
    **/
    count<T extends ServiceStatusHistoryCountArgs>(
      args?: Subset<T, ServiceStatusHistoryCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ServiceStatusHistoryCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ServiceStatusHistory.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusHistoryAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ServiceStatusHistoryAggregateArgs>(args: Subset<T, ServiceStatusHistoryAggregateArgs>): Prisma.PrismaPromise<GetServiceStatusHistoryAggregateType<T>>

    /**
     * Group by ServiceStatusHistory.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ServiceStatusHistoryGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ServiceStatusHistoryGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ServiceStatusHistoryGroupByArgs['orderBy'] }
        : { orderBy?: ServiceStatusHistoryGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ServiceStatusHistoryGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetServiceStatusHistoryGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ServiceStatusHistory model
   */
  readonly fields: ServiceStatusHistoryFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ServiceStatusHistory.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ServiceStatusHistoryClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    service<T extends ServiceStatusDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ServiceStatusDefaultArgs<ExtArgs>>): Prisma__ServiceStatusClient<$Result.GetResult<Prisma.$ServiceStatusPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ServiceStatusHistory model
   */
  interface ServiceStatusHistoryFieldRefs {
    readonly id: FieldRef<"ServiceStatusHistory", 'String'>
    readonly serviceId: FieldRef<"ServiceStatusHistory", 'String'>
    readonly status: FieldRef<"ServiceStatusHistory", 'String'>
    readonly recordedAt: FieldRef<"ServiceStatusHistory", 'DateTime'>
    readonly createdAt: FieldRef<"ServiceStatusHistory", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * ServiceStatusHistory findUnique
   */
  export type ServiceStatusHistoryFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatusHistory to fetch.
     */
    where: ServiceStatusHistoryWhereUniqueInput
  }

  /**
   * ServiceStatusHistory findUniqueOrThrow
   */
  export type ServiceStatusHistoryFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatusHistory to fetch.
     */
    where: ServiceStatusHistoryWhereUniqueInput
  }

  /**
   * ServiceStatusHistory findFirst
   */
  export type ServiceStatusHistoryFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatusHistory to fetch.
     */
    where?: ServiceStatusHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatusHistories to fetch.
     */
    orderBy?: ServiceStatusHistoryOrderByWithRelationInput | ServiceStatusHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ServiceStatusHistories.
     */
    cursor?: ServiceStatusHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatusHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatusHistories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ServiceStatusHistories.
     */
    distinct?: ServiceStatusHistoryScalarFieldEnum | ServiceStatusHistoryScalarFieldEnum[]
  }

  /**
   * ServiceStatusHistory findFirstOrThrow
   */
  export type ServiceStatusHistoryFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatusHistory to fetch.
     */
    where?: ServiceStatusHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatusHistories to fetch.
     */
    orderBy?: ServiceStatusHistoryOrderByWithRelationInput | ServiceStatusHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ServiceStatusHistories.
     */
    cursor?: ServiceStatusHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatusHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatusHistories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ServiceStatusHistories.
     */
    distinct?: ServiceStatusHistoryScalarFieldEnum | ServiceStatusHistoryScalarFieldEnum[]
  }

  /**
   * ServiceStatusHistory findMany
   */
  export type ServiceStatusHistoryFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * Filter, which ServiceStatusHistories to fetch.
     */
    where?: ServiceStatusHistoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ServiceStatusHistories to fetch.
     */
    orderBy?: ServiceStatusHistoryOrderByWithRelationInput | ServiceStatusHistoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ServiceStatusHistories.
     */
    cursor?: ServiceStatusHistoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ServiceStatusHistories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ServiceStatusHistories.
     */
    skip?: number
    distinct?: ServiceStatusHistoryScalarFieldEnum | ServiceStatusHistoryScalarFieldEnum[]
  }

  /**
   * ServiceStatusHistory create
   */
  export type ServiceStatusHistoryCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * The data needed to create a ServiceStatusHistory.
     */
    data: XOR<ServiceStatusHistoryCreateInput, ServiceStatusHistoryUncheckedCreateInput>
  }

  /**
   * ServiceStatusHistory createMany
   */
  export type ServiceStatusHistoryCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ServiceStatusHistories.
     */
    data: ServiceStatusHistoryCreateManyInput | ServiceStatusHistoryCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * ServiceStatusHistory createManyAndReturn
   */
  export type ServiceStatusHistoryCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * The data used to create many ServiceStatusHistories.
     */
    data: ServiceStatusHistoryCreateManyInput | ServiceStatusHistoryCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * ServiceStatusHistory update
   */
  export type ServiceStatusHistoryUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * The data needed to update a ServiceStatusHistory.
     */
    data: XOR<ServiceStatusHistoryUpdateInput, ServiceStatusHistoryUncheckedUpdateInput>
    /**
     * Choose, which ServiceStatusHistory to update.
     */
    where: ServiceStatusHistoryWhereUniqueInput
  }

  /**
   * ServiceStatusHistory updateMany
   */
  export type ServiceStatusHistoryUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ServiceStatusHistories.
     */
    data: XOR<ServiceStatusHistoryUpdateManyMutationInput, ServiceStatusHistoryUncheckedUpdateManyInput>
    /**
     * Filter which ServiceStatusHistories to update
     */
    where?: ServiceStatusHistoryWhereInput
    /**
     * Limit how many ServiceStatusHistories to update.
     */
    limit?: number
  }

  /**
   * ServiceStatusHistory updateManyAndReturn
   */
  export type ServiceStatusHistoryUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * The data used to update ServiceStatusHistories.
     */
    data: XOR<ServiceStatusHistoryUpdateManyMutationInput, ServiceStatusHistoryUncheckedUpdateManyInput>
    /**
     * Filter which ServiceStatusHistories to update
     */
    where?: ServiceStatusHistoryWhereInput
    /**
     * Limit how many ServiceStatusHistories to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * ServiceStatusHistory upsert
   */
  export type ServiceStatusHistoryUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * The filter to search for the ServiceStatusHistory to update in case it exists.
     */
    where: ServiceStatusHistoryWhereUniqueInput
    /**
     * In case the ServiceStatusHistory found by the `where` argument doesn't exist, create a new ServiceStatusHistory with this data.
     */
    create: XOR<ServiceStatusHistoryCreateInput, ServiceStatusHistoryUncheckedCreateInput>
    /**
     * In case the ServiceStatusHistory was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ServiceStatusHistoryUpdateInput, ServiceStatusHistoryUncheckedUpdateInput>
  }

  /**
   * ServiceStatusHistory delete
   */
  export type ServiceStatusHistoryDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
    /**
     * Filter which ServiceStatusHistory to delete.
     */
    where: ServiceStatusHistoryWhereUniqueInput
  }

  /**
   * ServiceStatusHistory deleteMany
   */
  export type ServiceStatusHistoryDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ServiceStatusHistories to delete
     */
    where?: ServiceStatusHistoryWhereInput
    /**
     * Limit how many ServiceStatusHistories to delete.
     */
    limit?: number
  }

  /**
   * ServiceStatusHistory without action
   */
  export type ServiceStatusHistoryDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ServiceStatusHistory
     */
    select?: ServiceStatusHistorySelect<ExtArgs> | null
    /**
     * Omit specific fields from the ServiceStatusHistory
     */
    omit?: ServiceStatusHistoryOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ServiceStatusHistoryInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const WorkerProcessScalarFieldEnum: {
    id: 'id',
    type: 'type',
    status: 'status',
    data: 'data',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    startedAt: 'startedAt',
    completedAt: 'completedAt',
    error: 'error'
  };

  export type WorkerProcessScalarFieldEnum = (typeof WorkerProcessScalarFieldEnum)[keyof typeof WorkerProcessScalarFieldEnum]


  export const SearchJobScalarFieldEnum: {
    id: 'id',
    query: 'query',
    filters: 'filters',
    status: 'status',
    results: 'results',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    startedAt: 'startedAt',
    completedAt: 'completedAt',
    error: 'error'
  };

  export type SearchJobScalarFieldEnum = (typeof SearchJobScalarFieldEnum)[keyof typeof SearchJobScalarFieldEnum]


  export const AtsAnalysisScalarFieldEnum: {
    id: 'id',
    resumeId: 'resumeId',
    overallScore: 'overallScore',
    keywordScore: 'keywordScore',
    formatScore: 'formatScore',
    contentScore: 'contentScore',
    readabilityScore: 'readabilityScore',
    detectedIssues: 'detectedIssues',
    suggestedKeywords: 'suggestedKeywords',
    analysisDetails: 'analysisDetails',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type AtsAnalysisScalarFieldEnum = (typeof AtsAnalysisScalarFieldEnum)[keyof typeof AtsAnalysisScalarFieldEnum]


  export const ParsedResumeScalarFieldEnum: {
    id: 'id',
    resumeId: 'resumeId',
    userId: 'userId',
    profileId: 'profileId',
    parsedAt: 'parsedAt',
    parserVersion: 'parserVersion',
    parserType: 'parserType',
    fileType: 'fileType',
    parseTime: 'parseTime',
    status: 'status',
    name: 'name',
    email: 'email',
    phone: 'phone',
    location: 'location',
    summary: 'summary',
    website: 'website',
    education: 'education',
    experience: 'experience',
    skills: 'skills',
    projects: 'projects',
    certifications: 'certifications',
    languages: 'languages',
    publications: 'publications',
    achievements: 'achievements',
    volunteer: 'volunteer',
    interests: 'interests',
    references: 'references',
    patents: 'patents',
    rawText: 'rawText',
    sectionMap: 'sectionMap',
    confidenceScores: 'confidenceScores',
    overallScore: 'overallScore'
  };

  export type ParsedResumeScalarFieldEnum = (typeof ParsedResumeScalarFieldEnum)[keyof typeof ParsedResumeScalarFieldEnum]


  export const ServiceStatusScalarFieldEnum: {
    id: 'id',
    name: 'name',
    status: 'status',
    description: 'description',
    lastCheckedAt: 'lastCheckedAt',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type ServiceStatusScalarFieldEnum = (typeof ServiceStatusScalarFieldEnum)[keyof typeof ServiceStatusScalarFieldEnum]


  export const ServiceStatusHistoryScalarFieldEnum: {
    id: 'id',
    serviceId: 'serviceId',
    status: 'status',
    recordedAt: 'recordedAt',
    createdAt: 'createdAt'
  };

  export type ServiceStatusHistoryScalarFieldEnum = (typeof ServiceStatusHistoryScalarFieldEnum)[keyof typeof ServiceStatusHistoryScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    
  /**
   * Deep Input Types
   */


  export type WorkerProcessWhereInput = {
    AND?: WorkerProcessWhereInput | WorkerProcessWhereInput[]
    OR?: WorkerProcessWhereInput[]
    NOT?: WorkerProcessWhereInput | WorkerProcessWhereInput[]
    id?: StringFilter<"WorkerProcess"> | string
    type?: StringFilter<"WorkerProcess"> | string
    status?: StringFilter<"WorkerProcess"> | string
    data?: JsonNullableFilter<"WorkerProcess">
    createdAt?: DateTimeFilter<"WorkerProcess"> | Date | string
    updatedAt?: DateTimeFilter<"WorkerProcess"> | Date | string
    startedAt?: DateTimeNullableFilter<"WorkerProcess"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"WorkerProcess"> | Date | string | null
    error?: StringNullableFilter<"WorkerProcess"> | string | null
  }

  export type WorkerProcessOrderByWithRelationInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    data?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    error?: SortOrderInput | SortOrder
  }

  export type WorkerProcessWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: WorkerProcessWhereInput | WorkerProcessWhereInput[]
    OR?: WorkerProcessWhereInput[]
    NOT?: WorkerProcessWhereInput | WorkerProcessWhereInput[]
    type?: StringFilter<"WorkerProcess"> | string
    status?: StringFilter<"WorkerProcess"> | string
    data?: JsonNullableFilter<"WorkerProcess">
    createdAt?: DateTimeFilter<"WorkerProcess"> | Date | string
    updatedAt?: DateTimeFilter<"WorkerProcess"> | Date | string
    startedAt?: DateTimeNullableFilter<"WorkerProcess"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"WorkerProcess"> | Date | string | null
    error?: StringNullableFilter<"WorkerProcess"> | string | null
  }, "id">

  export type WorkerProcessOrderByWithAggregationInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    data?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    error?: SortOrderInput | SortOrder
    _count?: WorkerProcessCountOrderByAggregateInput
    _max?: WorkerProcessMaxOrderByAggregateInput
    _min?: WorkerProcessMinOrderByAggregateInput
  }

  export type WorkerProcessScalarWhereWithAggregatesInput = {
    AND?: WorkerProcessScalarWhereWithAggregatesInput | WorkerProcessScalarWhereWithAggregatesInput[]
    OR?: WorkerProcessScalarWhereWithAggregatesInput[]
    NOT?: WorkerProcessScalarWhereWithAggregatesInput | WorkerProcessScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"WorkerProcess"> | string
    type?: StringWithAggregatesFilter<"WorkerProcess"> | string
    status?: StringWithAggregatesFilter<"WorkerProcess"> | string
    data?: JsonNullableWithAggregatesFilter<"WorkerProcess">
    createdAt?: DateTimeWithAggregatesFilter<"WorkerProcess"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"WorkerProcess"> | Date | string
    startedAt?: DateTimeNullableWithAggregatesFilter<"WorkerProcess"> | Date | string | null
    completedAt?: DateTimeNullableWithAggregatesFilter<"WorkerProcess"> | Date | string | null
    error?: StringNullableWithAggregatesFilter<"WorkerProcess"> | string | null
  }

  export type SearchJobWhereInput = {
    AND?: SearchJobWhereInput | SearchJobWhereInput[]
    OR?: SearchJobWhereInput[]
    NOT?: SearchJobWhereInput | SearchJobWhereInput[]
    id?: StringFilter<"SearchJob"> | string
    query?: StringFilter<"SearchJob"> | string
    filters?: JsonNullableFilter<"SearchJob">
    status?: StringFilter<"SearchJob"> | string
    results?: JsonNullableFilter<"SearchJob">
    createdAt?: DateTimeFilter<"SearchJob"> | Date | string
    updatedAt?: DateTimeFilter<"SearchJob"> | Date | string
    startedAt?: DateTimeNullableFilter<"SearchJob"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"SearchJob"> | Date | string | null
    error?: StringNullableFilter<"SearchJob"> | string | null
  }

  export type SearchJobOrderByWithRelationInput = {
    id?: SortOrder
    query?: SortOrder
    filters?: SortOrderInput | SortOrder
    status?: SortOrder
    results?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    error?: SortOrderInput | SortOrder
  }

  export type SearchJobWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: SearchJobWhereInput | SearchJobWhereInput[]
    OR?: SearchJobWhereInput[]
    NOT?: SearchJobWhereInput | SearchJobWhereInput[]
    query?: StringFilter<"SearchJob"> | string
    filters?: JsonNullableFilter<"SearchJob">
    status?: StringFilter<"SearchJob"> | string
    results?: JsonNullableFilter<"SearchJob">
    createdAt?: DateTimeFilter<"SearchJob"> | Date | string
    updatedAt?: DateTimeFilter<"SearchJob"> | Date | string
    startedAt?: DateTimeNullableFilter<"SearchJob"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"SearchJob"> | Date | string | null
    error?: StringNullableFilter<"SearchJob"> | string | null
  }, "id">

  export type SearchJobOrderByWithAggregationInput = {
    id?: SortOrder
    query?: SortOrder
    filters?: SortOrderInput | SortOrder
    status?: SortOrder
    results?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    error?: SortOrderInput | SortOrder
    _count?: SearchJobCountOrderByAggregateInput
    _max?: SearchJobMaxOrderByAggregateInput
    _min?: SearchJobMinOrderByAggregateInput
  }

  export type SearchJobScalarWhereWithAggregatesInput = {
    AND?: SearchJobScalarWhereWithAggregatesInput | SearchJobScalarWhereWithAggregatesInput[]
    OR?: SearchJobScalarWhereWithAggregatesInput[]
    NOT?: SearchJobScalarWhereWithAggregatesInput | SearchJobScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"SearchJob"> | string
    query?: StringWithAggregatesFilter<"SearchJob"> | string
    filters?: JsonNullableWithAggregatesFilter<"SearchJob">
    status?: StringWithAggregatesFilter<"SearchJob"> | string
    results?: JsonNullableWithAggregatesFilter<"SearchJob">
    createdAt?: DateTimeWithAggregatesFilter<"SearchJob"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"SearchJob"> | Date | string
    startedAt?: DateTimeNullableWithAggregatesFilter<"SearchJob"> | Date | string | null
    completedAt?: DateTimeNullableWithAggregatesFilter<"SearchJob"> | Date | string | null
    error?: StringNullableWithAggregatesFilter<"SearchJob"> | string | null
  }

  export type AtsAnalysisWhereInput = {
    AND?: AtsAnalysisWhereInput | AtsAnalysisWhereInput[]
    OR?: AtsAnalysisWhereInput[]
    NOT?: AtsAnalysisWhereInput | AtsAnalysisWhereInput[]
    id?: StringFilter<"AtsAnalysis"> | string
    resumeId?: StringFilter<"AtsAnalysis"> | string
    overallScore?: FloatFilter<"AtsAnalysis"> | number
    keywordScore?: FloatFilter<"AtsAnalysis"> | number
    formatScore?: FloatFilter<"AtsAnalysis"> | number
    contentScore?: FloatFilter<"AtsAnalysis"> | number
    readabilityScore?: FloatFilter<"AtsAnalysis"> | number
    detectedIssues?: JsonFilter<"AtsAnalysis">
    suggestedKeywords?: JsonFilter<"AtsAnalysis">
    analysisDetails?: JsonNullableFilter<"AtsAnalysis">
    createdAt?: DateTimeFilter<"AtsAnalysis"> | Date | string
    updatedAt?: DateTimeFilter<"AtsAnalysis"> | Date | string
  }

  export type AtsAnalysisOrderByWithRelationInput = {
    id?: SortOrder
    resumeId?: SortOrder
    overallScore?: SortOrder
    keywordScore?: SortOrder
    formatScore?: SortOrder
    contentScore?: SortOrder
    readabilityScore?: SortOrder
    detectedIssues?: SortOrder
    suggestedKeywords?: SortOrder
    analysisDetails?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AtsAnalysisWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    resumeId?: string
    AND?: AtsAnalysisWhereInput | AtsAnalysisWhereInput[]
    OR?: AtsAnalysisWhereInput[]
    NOT?: AtsAnalysisWhereInput | AtsAnalysisWhereInput[]
    overallScore?: FloatFilter<"AtsAnalysis"> | number
    keywordScore?: FloatFilter<"AtsAnalysis"> | number
    formatScore?: FloatFilter<"AtsAnalysis"> | number
    contentScore?: FloatFilter<"AtsAnalysis"> | number
    readabilityScore?: FloatFilter<"AtsAnalysis"> | number
    detectedIssues?: JsonFilter<"AtsAnalysis">
    suggestedKeywords?: JsonFilter<"AtsAnalysis">
    analysisDetails?: JsonNullableFilter<"AtsAnalysis">
    createdAt?: DateTimeFilter<"AtsAnalysis"> | Date | string
    updatedAt?: DateTimeFilter<"AtsAnalysis"> | Date | string
  }, "id" | "resumeId">

  export type AtsAnalysisOrderByWithAggregationInput = {
    id?: SortOrder
    resumeId?: SortOrder
    overallScore?: SortOrder
    keywordScore?: SortOrder
    formatScore?: SortOrder
    contentScore?: SortOrder
    readabilityScore?: SortOrder
    detectedIssues?: SortOrder
    suggestedKeywords?: SortOrder
    analysisDetails?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: AtsAnalysisCountOrderByAggregateInput
    _avg?: AtsAnalysisAvgOrderByAggregateInput
    _max?: AtsAnalysisMaxOrderByAggregateInput
    _min?: AtsAnalysisMinOrderByAggregateInput
    _sum?: AtsAnalysisSumOrderByAggregateInput
  }

  export type AtsAnalysisScalarWhereWithAggregatesInput = {
    AND?: AtsAnalysisScalarWhereWithAggregatesInput | AtsAnalysisScalarWhereWithAggregatesInput[]
    OR?: AtsAnalysisScalarWhereWithAggregatesInput[]
    NOT?: AtsAnalysisScalarWhereWithAggregatesInput | AtsAnalysisScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"AtsAnalysis"> | string
    resumeId?: StringWithAggregatesFilter<"AtsAnalysis"> | string
    overallScore?: FloatWithAggregatesFilter<"AtsAnalysis"> | number
    keywordScore?: FloatWithAggregatesFilter<"AtsAnalysis"> | number
    formatScore?: FloatWithAggregatesFilter<"AtsAnalysis"> | number
    contentScore?: FloatWithAggregatesFilter<"AtsAnalysis"> | number
    readabilityScore?: FloatWithAggregatesFilter<"AtsAnalysis"> | number
    detectedIssues?: JsonWithAggregatesFilter<"AtsAnalysis">
    suggestedKeywords?: JsonWithAggregatesFilter<"AtsAnalysis">
    analysisDetails?: JsonNullableWithAggregatesFilter<"AtsAnalysis">
    createdAt?: DateTimeWithAggregatesFilter<"AtsAnalysis"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"AtsAnalysis"> | Date | string
  }

  export type ParsedResumeWhereInput = {
    AND?: ParsedResumeWhereInput | ParsedResumeWhereInput[]
    OR?: ParsedResumeWhereInput[]
    NOT?: ParsedResumeWhereInput | ParsedResumeWhereInput[]
    id?: StringFilter<"ParsedResume"> | string
    resumeId?: StringFilter<"ParsedResume"> | string
    userId?: StringFilter<"ParsedResume"> | string
    profileId?: StringNullableFilter<"ParsedResume"> | string | null
    parsedAt?: DateTimeFilter<"ParsedResume"> | Date | string
    parserVersion?: StringFilter<"ParsedResume"> | string
    parserType?: StringFilter<"ParsedResume"> | string
    fileType?: StringNullableFilter<"ParsedResume"> | string | null
    parseTime?: IntNullableFilter<"ParsedResume"> | number | null
    status?: StringFilter<"ParsedResume"> | string
    name?: StringNullableFilter<"ParsedResume"> | string | null
    email?: StringNullableFilter<"ParsedResume"> | string | null
    phone?: StringNullableFilter<"ParsedResume"> | string | null
    location?: StringNullableFilter<"ParsedResume"> | string | null
    summary?: StringNullableFilter<"ParsedResume"> | string | null
    website?: StringNullableFilter<"ParsedResume"> | string | null
    education?: JsonNullableFilter<"ParsedResume">
    experience?: JsonNullableFilter<"ParsedResume">
    skills?: JsonNullableFilter<"ParsedResume">
    projects?: JsonNullableFilter<"ParsedResume">
    certifications?: JsonNullableFilter<"ParsedResume">
    languages?: JsonNullableFilter<"ParsedResume">
    publications?: JsonNullableFilter<"ParsedResume">
    achievements?: JsonNullableFilter<"ParsedResume">
    volunteer?: JsonNullableFilter<"ParsedResume">
    interests?: JsonNullableFilter<"ParsedResume">
    references?: JsonNullableFilter<"ParsedResume">
    patents?: JsonNullableFilter<"ParsedResume">
    rawText?: StringNullableFilter<"ParsedResume"> | string | null
    sectionMap?: JsonNullableFilter<"ParsedResume">
    confidenceScores?: JsonNullableFilter<"ParsedResume">
    overallScore?: FloatNullableFilter<"ParsedResume"> | number | null
  }

  export type ParsedResumeOrderByWithRelationInput = {
    id?: SortOrder
    resumeId?: SortOrder
    userId?: SortOrder
    profileId?: SortOrderInput | SortOrder
    parsedAt?: SortOrder
    parserVersion?: SortOrder
    parserType?: SortOrder
    fileType?: SortOrderInput | SortOrder
    parseTime?: SortOrderInput | SortOrder
    status?: SortOrder
    name?: SortOrderInput | SortOrder
    email?: SortOrderInput | SortOrder
    phone?: SortOrderInput | SortOrder
    location?: SortOrderInput | SortOrder
    summary?: SortOrderInput | SortOrder
    website?: SortOrderInput | SortOrder
    education?: SortOrderInput | SortOrder
    experience?: SortOrderInput | SortOrder
    skills?: SortOrderInput | SortOrder
    projects?: SortOrderInput | SortOrder
    certifications?: SortOrderInput | SortOrder
    languages?: SortOrderInput | SortOrder
    publications?: SortOrderInput | SortOrder
    achievements?: SortOrderInput | SortOrder
    volunteer?: SortOrderInput | SortOrder
    interests?: SortOrderInput | SortOrder
    references?: SortOrderInput | SortOrder
    patents?: SortOrderInput | SortOrder
    rawText?: SortOrderInput | SortOrder
    sectionMap?: SortOrderInput | SortOrder
    confidenceScores?: SortOrderInput | SortOrder
    overallScore?: SortOrderInput | SortOrder
  }

  export type ParsedResumeWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    resumeId?: string
    AND?: ParsedResumeWhereInput | ParsedResumeWhereInput[]
    OR?: ParsedResumeWhereInput[]
    NOT?: ParsedResumeWhereInput | ParsedResumeWhereInput[]
    userId?: StringFilter<"ParsedResume"> | string
    profileId?: StringNullableFilter<"ParsedResume"> | string | null
    parsedAt?: DateTimeFilter<"ParsedResume"> | Date | string
    parserVersion?: StringFilter<"ParsedResume"> | string
    parserType?: StringFilter<"ParsedResume"> | string
    fileType?: StringNullableFilter<"ParsedResume"> | string | null
    parseTime?: IntNullableFilter<"ParsedResume"> | number | null
    status?: StringFilter<"ParsedResume"> | string
    name?: StringNullableFilter<"ParsedResume"> | string | null
    email?: StringNullableFilter<"ParsedResume"> | string | null
    phone?: StringNullableFilter<"ParsedResume"> | string | null
    location?: StringNullableFilter<"ParsedResume"> | string | null
    summary?: StringNullableFilter<"ParsedResume"> | string | null
    website?: StringNullableFilter<"ParsedResume"> | string | null
    education?: JsonNullableFilter<"ParsedResume">
    experience?: JsonNullableFilter<"ParsedResume">
    skills?: JsonNullableFilter<"ParsedResume">
    projects?: JsonNullableFilter<"ParsedResume">
    certifications?: JsonNullableFilter<"ParsedResume">
    languages?: JsonNullableFilter<"ParsedResume">
    publications?: JsonNullableFilter<"ParsedResume">
    achievements?: JsonNullableFilter<"ParsedResume">
    volunteer?: JsonNullableFilter<"ParsedResume">
    interests?: JsonNullableFilter<"ParsedResume">
    references?: JsonNullableFilter<"ParsedResume">
    patents?: JsonNullableFilter<"ParsedResume">
    rawText?: StringNullableFilter<"ParsedResume"> | string | null
    sectionMap?: JsonNullableFilter<"ParsedResume">
    confidenceScores?: JsonNullableFilter<"ParsedResume">
    overallScore?: FloatNullableFilter<"ParsedResume"> | number | null
  }, "id" | "resumeId">

  export type ParsedResumeOrderByWithAggregationInput = {
    id?: SortOrder
    resumeId?: SortOrder
    userId?: SortOrder
    profileId?: SortOrderInput | SortOrder
    parsedAt?: SortOrder
    parserVersion?: SortOrder
    parserType?: SortOrder
    fileType?: SortOrderInput | SortOrder
    parseTime?: SortOrderInput | SortOrder
    status?: SortOrder
    name?: SortOrderInput | SortOrder
    email?: SortOrderInput | SortOrder
    phone?: SortOrderInput | SortOrder
    location?: SortOrderInput | SortOrder
    summary?: SortOrderInput | SortOrder
    website?: SortOrderInput | SortOrder
    education?: SortOrderInput | SortOrder
    experience?: SortOrderInput | SortOrder
    skills?: SortOrderInput | SortOrder
    projects?: SortOrderInput | SortOrder
    certifications?: SortOrderInput | SortOrder
    languages?: SortOrderInput | SortOrder
    publications?: SortOrderInput | SortOrder
    achievements?: SortOrderInput | SortOrder
    volunteer?: SortOrderInput | SortOrder
    interests?: SortOrderInput | SortOrder
    references?: SortOrderInput | SortOrder
    patents?: SortOrderInput | SortOrder
    rawText?: SortOrderInput | SortOrder
    sectionMap?: SortOrderInput | SortOrder
    confidenceScores?: SortOrderInput | SortOrder
    overallScore?: SortOrderInput | SortOrder
    _count?: ParsedResumeCountOrderByAggregateInput
    _avg?: ParsedResumeAvgOrderByAggregateInput
    _max?: ParsedResumeMaxOrderByAggregateInput
    _min?: ParsedResumeMinOrderByAggregateInput
    _sum?: ParsedResumeSumOrderByAggregateInput
  }

  export type ParsedResumeScalarWhereWithAggregatesInput = {
    AND?: ParsedResumeScalarWhereWithAggregatesInput | ParsedResumeScalarWhereWithAggregatesInput[]
    OR?: ParsedResumeScalarWhereWithAggregatesInput[]
    NOT?: ParsedResumeScalarWhereWithAggregatesInput | ParsedResumeScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"ParsedResume"> | string
    resumeId?: StringWithAggregatesFilter<"ParsedResume"> | string
    userId?: StringWithAggregatesFilter<"ParsedResume"> | string
    profileId?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    parsedAt?: DateTimeWithAggregatesFilter<"ParsedResume"> | Date | string
    parserVersion?: StringWithAggregatesFilter<"ParsedResume"> | string
    parserType?: StringWithAggregatesFilter<"ParsedResume"> | string
    fileType?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    parseTime?: IntNullableWithAggregatesFilter<"ParsedResume"> | number | null
    status?: StringWithAggregatesFilter<"ParsedResume"> | string
    name?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    email?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    phone?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    location?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    summary?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    website?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    education?: JsonNullableWithAggregatesFilter<"ParsedResume">
    experience?: JsonNullableWithAggregatesFilter<"ParsedResume">
    skills?: JsonNullableWithAggregatesFilter<"ParsedResume">
    projects?: JsonNullableWithAggregatesFilter<"ParsedResume">
    certifications?: JsonNullableWithAggregatesFilter<"ParsedResume">
    languages?: JsonNullableWithAggregatesFilter<"ParsedResume">
    publications?: JsonNullableWithAggregatesFilter<"ParsedResume">
    achievements?: JsonNullableWithAggregatesFilter<"ParsedResume">
    volunteer?: JsonNullableWithAggregatesFilter<"ParsedResume">
    interests?: JsonNullableWithAggregatesFilter<"ParsedResume">
    references?: JsonNullableWithAggregatesFilter<"ParsedResume">
    patents?: JsonNullableWithAggregatesFilter<"ParsedResume">
    rawText?: StringNullableWithAggregatesFilter<"ParsedResume"> | string | null
    sectionMap?: JsonNullableWithAggregatesFilter<"ParsedResume">
    confidenceScores?: JsonNullableWithAggregatesFilter<"ParsedResume">
    overallScore?: FloatNullableWithAggregatesFilter<"ParsedResume"> | number | null
  }

  export type ServiceStatusWhereInput = {
    AND?: ServiceStatusWhereInput | ServiceStatusWhereInput[]
    OR?: ServiceStatusWhereInput[]
    NOT?: ServiceStatusWhereInput | ServiceStatusWhereInput[]
    id?: StringFilter<"ServiceStatus"> | string
    name?: StringFilter<"ServiceStatus"> | string
    status?: StringFilter<"ServiceStatus"> | string
    description?: StringNullableFilter<"ServiceStatus"> | string | null
    lastCheckedAt?: DateTimeFilter<"ServiceStatus"> | Date | string
    createdAt?: DateTimeFilter<"ServiceStatus"> | Date | string
    updatedAt?: DateTimeFilter<"ServiceStatus"> | Date | string
    statusHistory?: ServiceStatusHistoryListRelationFilter
  }

  export type ServiceStatusOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    description?: SortOrderInput | SortOrder
    lastCheckedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    statusHistory?: ServiceStatusHistoryOrderByRelationAggregateInput
  }

  export type ServiceStatusWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    name?: string
    AND?: ServiceStatusWhereInput | ServiceStatusWhereInput[]
    OR?: ServiceStatusWhereInput[]
    NOT?: ServiceStatusWhereInput | ServiceStatusWhereInput[]
    status?: StringFilter<"ServiceStatus"> | string
    description?: StringNullableFilter<"ServiceStatus"> | string | null
    lastCheckedAt?: DateTimeFilter<"ServiceStatus"> | Date | string
    createdAt?: DateTimeFilter<"ServiceStatus"> | Date | string
    updatedAt?: DateTimeFilter<"ServiceStatus"> | Date | string
    statusHistory?: ServiceStatusHistoryListRelationFilter
  }, "id" | "name">

  export type ServiceStatusOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    description?: SortOrderInput | SortOrder
    lastCheckedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: ServiceStatusCountOrderByAggregateInput
    _max?: ServiceStatusMaxOrderByAggregateInput
    _min?: ServiceStatusMinOrderByAggregateInput
  }

  export type ServiceStatusScalarWhereWithAggregatesInput = {
    AND?: ServiceStatusScalarWhereWithAggregatesInput | ServiceStatusScalarWhereWithAggregatesInput[]
    OR?: ServiceStatusScalarWhereWithAggregatesInput[]
    NOT?: ServiceStatusScalarWhereWithAggregatesInput | ServiceStatusScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"ServiceStatus"> | string
    name?: StringWithAggregatesFilter<"ServiceStatus"> | string
    status?: StringWithAggregatesFilter<"ServiceStatus"> | string
    description?: StringNullableWithAggregatesFilter<"ServiceStatus"> | string | null
    lastCheckedAt?: DateTimeWithAggregatesFilter<"ServiceStatus"> | Date | string
    createdAt?: DateTimeWithAggregatesFilter<"ServiceStatus"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"ServiceStatus"> | Date | string
  }

  export type ServiceStatusHistoryWhereInput = {
    AND?: ServiceStatusHistoryWhereInput | ServiceStatusHistoryWhereInput[]
    OR?: ServiceStatusHistoryWhereInput[]
    NOT?: ServiceStatusHistoryWhereInput | ServiceStatusHistoryWhereInput[]
    id?: StringFilter<"ServiceStatusHistory"> | string
    serviceId?: StringFilter<"ServiceStatusHistory"> | string
    status?: StringFilter<"ServiceStatusHistory"> | string
    recordedAt?: DateTimeFilter<"ServiceStatusHistory"> | Date | string
    createdAt?: DateTimeFilter<"ServiceStatusHistory"> | Date | string
    service?: XOR<ServiceStatusScalarRelationFilter, ServiceStatusWhereInput>
  }

  export type ServiceStatusHistoryOrderByWithRelationInput = {
    id?: SortOrder
    serviceId?: SortOrder
    status?: SortOrder
    recordedAt?: SortOrder
    createdAt?: SortOrder
    service?: ServiceStatusOrderByWithRelationInput
  }

  export type ServiceStatusHistoryWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: ServiceStatusHistoryWhereInput | ServiceStatusHistoryWhereInput[]
    OR?: ServiceStatusHistoryWhereInput[]
    NOT?: ServiceStatusHistoryWhereInput | ServiceStatusHistoryWhereInput[]
    serviceId?: StringFilter<"ServiceStatusHistory"> | string
    status?: StringFilter<"ServiceStatusHistory"> | string
    recordedAt?: DateTimeFilter<"ServiceStatusHistory"> | Date | string
    createdAt?: DateTimeFilter<"ServiceStatusHistory"> | Date | string
    service?: XOR<ServiceStatusScalarRelationFilter, ServiceStatusWhereInput>
  }, "id">

  export type ServiceStatusHistoryOrderByWithAggregationInput = {
    id?: SortOrder
    serviceId?: SortOrder
    status?: SortOrder
    recordedAt?: SortOrder
    createdAt?: SortOrder
    _count?: ServiceStatusHistoryCountOrderByAggregateInput
    _max?: ServiceStatusHistoryMaxOrderByAggregateInput
    _min?: ServiceStatusHistoryMinOrderByAggregateInput
  }

  export type ServiceStatusHistoryScalarWhereWithAggregatesInput = {
    AND?: ServiceStatusHistoryScalarWhereWithAggregatesInput | ServiceStatusHistoryScalarWhereWithAggregatesInput[]
    OR?: ServiceStatusHistoryScalarWhereWithAggregatesInput[]
    NOT?: ServiceStatusHistoryScalarWhereWithAggregatesInput | ServiceStatusHistoryScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"ServiceStatusHistory"> | string
    serviceId?: StringWithAggregatesFilter<"ServiceStatusHistory"> | string
    status?: StringWithAggregatesFilter<"ServiceStatusHistory"> | string
    recordedAt?: DateTimeWithAggregatesFilter<"ServiceStatusHistory"> | Date | string
    createdAt?: DateTimeWithAggregatesFilter<"ServiceStatusHistory"> | Date | string
  }

  export type WorkerProcessCreateInput = {
    id?: string
    type: string
    status: string
    data?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    error?: string | null
  }

  export type WorkerProcessUncheckedCreateInput = {
    id?: string
    type: string
    status: string
    data?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    error?: string | null
  }

  export type WorkerProcessUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type WorkerProcessUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type WorkerProcessCreateManyInput = {
    id?: string
    type: string
    status: string
    data?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    error?: string | null
  }

  export type WorkerProcessUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type WorkerProcessUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type SearchJobCreateInput = {
    id?: string
    query: string
    filters?: NullableJsonNullValueInput | InputJsonValue
    status: string
    results?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    error?: string | null
  }

  export type SearchJobUncheckedCreateInput = {
    id?: string
    query: string
    filters?: NullableJsonNullValueInput | InputJsonValue
    status: string
    results?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    error?: string | null
  }

  export type SearchJobUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    query?: StringFieldUpdateOperationsInput | string
    filters?: NullableJsonNullValueInput | InputJsonValue
    status?: StringFieldUpdateOperationsInput | string
    results?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type SearchJobUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    query?: StringFieldUpdateOperationsInput | string
    filters?: NullableJsonNullValueInput | InputJsonValue
    status?: StringFieldUpdateOperationsInput | string
    results?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type SearchJobCreateManyInput = {
    id?: string
    query: string
    filters?: NullableJsonNullValueInput | InputJsonValue
    status: string
    results?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    error?: string | null
  }

  export type SearchJobUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    query?: StringFieldUpdateOperationsInput | string
    filters?: NullableJsonNullValueInput | InputJsonValue
    status?: StringFieldUpdateOperationsInput | string
    results?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type SearchJobUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    query?: StringFieldUpdateOperationsInput | string
    filters?: NullableJsonNullValueInput | InputJsonValue
    status?: StringFieldUpdateOperationsInput | string
    results?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    error?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type AtsAnalysisCreateInput = {
    id?: string
    resumeId: string
    overallScore: number
    keywordScore: number
    formatScore: number
    contentScore: number
    readabilityScore: number
    detectedIssues: JsonNullValueInput | InputJsonValue
    suggestedKeywords: JsonNullValueInput | InputJsonValue
    analysisDetails?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AtsAnalysisUncheckedCreateInput = {
    id?: string
    resumeId: string
    overallScore: number
    keywordScore: number
    formatScore: number
    contentScore: number
    readabilityScore: number
    detectedIssues: JsonNullValueInput | InputJsonValue
    suggestedKeywords: JsonNullValueInput | InputJsonValue
    analysisDetails?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AtsAnalysisUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    overallScore?: FloatFieldUpdateOperationsInput | number
    keywordScore?: FloatFieldUpdateOperationsInput | number
    formatScore?: FloatFieldUpdateOperationsInput | number
    contentScore?: FloatFieldUpdateOperationsInput | number
    readabilityScore?: FloatFieldUpdateOperationsInput | number
    detectedIssues?: JsonNullValueInput | InputJsonValue
    suggestedKeywords?: JsonNullValueInput | InputJsonValue
    analysisDetails?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AtsAnalysisUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    overallScore?: FloatFieldUpdateOperationsInput | number
    keywordScore?: FloatFieldUpdateOperationsInput | number
    formatScore?: FloatFieldUpdateOperationsInput | number
    contentScore?: FloatFieldUpdateOperationsInput | number
    readabilityScore?: FloatFieldUpdateOperationsInput | number
    detectedIssues?: JsonNullValueInput | InputJsonValue
    suggestedKeywords?: JsonNullValueInput | InputJsonValue
    analysisDetails?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AtsAnalysisCreateManyInput = {
    id?: string
    resumeId: string
    overallScore: number
    keywordScore: number
    formatScore: number
    contentScore: number
    readabilityScore: number
    detectedIssues: JsonNullValueInput | InputJsonValue
    suggestedKeywords: JsonNullValueInput | InputJsonValue
    analysisDetails?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AtsAnalysisUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    overallScore?: FloatFieldUpdateOperationsInput | number
    keywordScore?: FloatFieldUpdateOperationsInput | number
    formatScore?: FloatFieldUpdateOperationsInput | number
    contentScore?: FloatFieldUpdateOperationsInput | number
    readabilityScore?: FloatFieldUpdateOperationsInput | number
    detectedIssues?: JsonNullValueInput | InputJsonValue
    suggestedKeywords?: JsonNullValueInput | InputJsonValue
    analysisDetails?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AtsAnalysisUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    overallScore?: FloatFieldUpdateOperationsInput | number
    keywordScore?: FloatFieldUpdateOperationsInput | number
    formatScore?: FloatFieldUpdateOperationsInput | number
    contentScore?: FloatFieldUpdateOperationsInput | number
    readabilityScore?: FloatFieldUpdateOperationsInput | number
    detectedIssues?: JsonNullValueInput | InputJsonValue
    suggestedKeywords?: JsonNullValueInput | InputJsonValue
    analysisDetails?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ParsedResumeCreateInput = {
    id: string
    resumeId: string
    userId: string
    profileId?: string | null
    parsedAt?: Date | string
    parserVersion: string
    parserType?: string
    fileType?: string | null
    parseTime?: number | null
    status?: string
    name?: string | null
    email?: string | null
    phone?: string | null
    location?: string | null
    summary?: string | null
    website?: string | null
    education?: NullableJsonNullValueInput | InputJsonValue
    experience?: NullableJsonNullValueInput | InputJsonValue
    skills?: NullableJsonNullValueInput | InputJsonValue
    projects?: NullableJsonNullValueInput | InputJsonValue
    certifications?: NullableJsonNullValueInput | InputJsonValue
    languages?: NullableJsonNullValueInput | InputJsonValue
    publications?: NullableJsonNullValueInput | InputJsonValue
    achievements?: NullableJsonNullValueInput | InputJsonValue
    volunteer?: NullableJsonNullValueInput | InputJsonValue
    interests?: NullableJsonNullValueInput | InputJsonValue
    references?: NullableJsonNullValueInput | InputJsonValue
    patents?: NullableJsonNullValueInput | InputJsonValue
    rawText?: string | null
    sectionMap?: NullableJsonNullValueInput | InputJsonValue
    confidenceScores?: NullableJsonNullValueInput | InputJsonValue
    overallScore?: number | null
  }

  export type ParsedResumeUncheckedCreateInput = {
    id: string
    resumeId: string
    userId: string
    profileId?: string | null
    parsedAt?: Date | string
    parserVersion: string
    parserType?: string
    fileType?: string | null
    parseTime?: number | null
    status?: string
    name?: string | null
    email?: string | null
    phone?: string | null
    location?: string | null
    summary?: string | null
    website?: string | null
    education?: NullableJsonNullValueInput | InputJsonValue
    experience?: NullableJsonNullValueInput | InputJsonValue
    skills?: NullableJsonNullValueInput | InputJsonValue
    projects?: NullableJsonNullValueInput | InputJsonValue
    certifications?: NullableJsonNullValueInput | InputJsonValue
    languages?: NullableJsonNullValueInput | InputJsonValue
    publications?: NullableJsonNullValueInput | InputJsonValue
    achievements?: NullableJsonNullValueInput | InputJsonValue
    volunteer?: NullableJsonNullValueInput | InputJsonValue
    interests?: NullableJsonNullValueInput | InputJsonValue
    references?: NullableJsonNullValueInput | InputJsonValue
    patents?: NullableJsonNullValueInput | InputJsonValue
    rawText?: string | null
    sectionMap?: NullableJsonNullValueInput | InputJsonValue
    confidenceScores?: NullableJsonNullValueInput | InputJsonValue
    overallScore?: number | null
  }

  export type ParsedResumeUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    profileId?: NullableStringFieldUpdateOperationsInput | string | null
    parsedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    parserVersion?: StringFieldUpdateOperationsInput | string
    parserType?: StringFieldUpdateOperationsInput | string
    fileType?: NullableStringFieldUpdateOperationsInput | string | null
    parseTime?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    location?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    education?: NullableJsonNullValueInput | InputJsonValue
    experience?: NullableJsonNullValueInput | InputJsonValue
    skills?: NullableJsonNullValueInput | InputJsonValue
    projects?: NullableJsonNullValueInput | InputJsonValue
    certifications?: NullableJsonNullValueInput | InputJsonValue
    languages?: NullableJsonNullValueInput | InputJsonValue
    publications?: NullableJsonNullValueInput | InputJsonValue
    achievements?: NullableJsonNullValueInput | InputJsonValue
    volunteer?: NullableJsonNullValueInput | InputJsonValue
    interests?: NullableJsonNullValueInput | InputJsonValue
    references?: NullableJsonNullValueInput | InputJsonValue
    patents?: NullableJsonNullValueInput | InputJsonValue
    rawText?: NullableStringFieldUpdateOperationsInput | string | null
    sectionMap?: NullableJsonNullValueInput | InputJsonValue
    confidenceScores?: NullableJsonNullValueInput | InputJsonValue
    overallScore?: NullableFloatFieldUpdateOperationsInput | number | null
  }

  export type ParsedResumeUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    profileId?: NullableStringFieldUpdateOperationsInput | string | null
    parsedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    parserVersion?: StringFieldUpdateOperationsInput | string
    parserType?: StringFieldUpdateOperationsInput | string
    fileType?: NullableStringFieldUpdateOperationsInput | string | null
    parseTime?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    location?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    education?: NullableJsonNullValueInput | InputJsonValue
    experience?: NullableJsonNullValueInput | InputJsonValue
    skills?: NullableJsonNullValueInput | InputJsonValue
    projects?: NullableJsonNullValueInput | InputJsonValue
    certifications?: NullableJsonNullValueInput | InputJsonValue
    languages?: NullableJsonNullValueInput | InputJsonValue
    publications?: NullableJsonNullValueInput | InputJsonValue
    achievements?: NullableJsonNullValueInput | InputJsonValue
    volunteer?: NullableJsonNullValueInput | InputJsonValue
    interests?: NullableJsonNullValueInput | InputJsonValue
    references?: NullableJsonNullValueInput | InputJsonValue
    patents?: NullableJsonNullValueInput | InputJsonValue
    rawText?: NullableStringFieldUpdateOperationsInput | string | null
    sectionMap?: NullableJsonNullValueInput | InputJsonValue
    confidenceScores?: NullableJsonNullValueInput | InputJsonValue
    overallScore?: NullableFloatFieldUpdateOperationsInput | number | null
  }

  export type ParsedResumeCreateManyInput = {
    id: string
    resumeId: string
    userId: string
    profileId?: string | null
    parsedAt?: Date | string
    parserVersion: string
    parserType?: string
    fileType?: string | null
    parseTime?: number | null
    status?: string
    name?: string | null
    email?: string | null
    phone?: string | null
    location?: string | null
    summary?: string | null
    website?: string | null
    education?: NullableJsonNullValueInput | InputJsonValue
    experience?: NullableJsonNullValueInput | InputJsonValue
    skills?: NullableJsonNullValueInput | InputJsonValue
    projects?: NullableJsonNullValueInput | InputJsonValue
    certifications?: NullableJsonNullValueInput | InputJsonValue
    languages?: NullableJsonNullValueInput | InputJsonValue
    publications?: NullableJsonNullValueInput | InputJsonValue
    achievements?: NullableJsonNullValueInput | InputJsonValue
    volunteer?: NullableJsonNullValueInput | InputJsonValue
    interests?: NullableJsonNullValueInput | InputJsonValue
    references?: NullableJsonNullValueInput | InputJsonValue
    patents?: NullableJsonNullValueInput | InputJsonValue
    rawText?: string | null
    sectionMap?: NullableJsonNullValueInput | InputJsonValue
    confidenceScores?: NullableJsonNullValueInput | InputJsonValue
    overallScore?: number | null
  }

  export type ParsedResumeUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    profileId?: NullableStringFieldUpdateOperationsInput | string | null
    parsedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    parserVersion?: StringFieldUpdateOperationsInput | string
    parserType?: StringFieldUpdateOperationsInput | string
    fileType?: NullableStringFieldUpdateOperationsInput | string | null
    parseTime?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    location?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    education?: NullableJsonNullValueInput | InputJsonValue
    experience?: NullableJsonNullValueInput | InputJsonValue
    skills?: NullableJsonNullValueInput | InputJsonValue
    projects?: NullableJsonNullValueInput | InputJsonValue
    certifications?: NullableJsonNullValueInput | InputJsonValue
    languages?: NullableJsonNullValueInput | InputJsonValue
    publications?: NullableJsonNullValueInput | InputJsonValue
    achievements?: NullableJsonNullValueInput | InputJsonValue
    volunteer?: NullableJsonNullValueInput | InputJsonValue
    interests?: NullableJsonNullValueInput | InputJsonValue
    references?: NullableJsonNullValueInput | InputJsonValue
    patents?: NullableJsonNullValueInput | InputJsonValue
    rawText?: NullableStringFieldUpdateOperationsInput | string | null
    sectionMap?: NullableJsonNullValueInput | InputJsonValue
    confidenceScores?: NullableJsonNullValueInput | InputJsonValue
    overallScore?: NullableFloatFieldUpdateOperationsInput | number | null
  }

  export type ParsedResumeUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    resumeId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    profileId?: NullableStringFieldUpdateOperationsInput | string | null
    parsedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    parserVersion?: StringFieldUpdateOperationsInput | string
    parserType?: StringFieldUpdateOperationsInput | string
    fileType?: NullableStringFieldUpdateOperationsInput | string | null
    parseTime?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    location?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    education?: NullableJsonNullValueInput | InputJsonValue
    experience?: NullableJsonNullValueInput | InputJsonValue
    skills?: NullableJsonNullValueInput | InputJsonValue
    projects?: NullableJsonNullValueInput | InputJsonValue
    certifications?: NullableJsonNullValueInput | InputJsonValue
    languages?: NullableJsonNullValueInput | InputJsonValue
    publications?: NullableJsonNullValueInput | InputJsonValue
    achievements?: NullableJsonNullValueInput | InputJsonValue
    volunteer?: NullableJsonNullValueInput | InputJsonValue
    interests?: NullableJsonNullValueInput | InputJsonValue
    references?: NullableJsonNullValueInput | InputJsonValue
    patents?: NullableJsonNullValueInput | InputJsonValue
    rawText?: NullableStringFieldUpdateOperationsInput | string | null
    sectionMap?: NullableJsonNullValueInput | InputJsonValue
    confidenceScores?: NullableJsonNullValueInput | InputJsonValue
    overallScore?: NullableFloatFieldUpdateOperationsInput | number | null
  }

  export type ServiceStatusCreateInput = {
    id?: string
    name: string
    status: string
    description?: string | null
    lastCheckedAt?: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    statusHistory?: ServiceStatusHistoryCreateNestedManyWithoutServiceInput
  }

  export type ServiceStatusUncheckedCreateInput = {
    id?: string
    name: string
    status: string
    description?: string | null
    lastCheckedAt?: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    statusHistory?: ServiceStatusHistoryUncheckedCreateNestedManyWithoutServiceInput
  }

  export type ServiceStatusUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    lastCheckedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    statusHistory?: ServiceStatusHistoryUpdateManyWithoutServiceNestedInput
  }

  export type ServiceStatusUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    lastCheckedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    statusHistory?: ServiceStatusHistoryUncheckedUpdateManyWithoutServiceNestedInput
  }

  export type ServiceStatusCreateManyInput = {
    id?: string
    name: string
    status: string
    description?: string | null
    lastCheckedAt?: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ServiceStatusUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    lastCheckedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    lastCheckedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusHistoryCreateInput = {
    id?: string
    status: string
    recordedAt?: Date | string
    createdAt?: Date | string
    service: ServiceStatusCreateNestedOneWithoutStatusHistoryInput
  }

  export type ServiceStatusHistoryUncheckedCreateInput = {
    id?: string
    serviceId: string
    status: string
    recordedAt?: Date | string
    createdAt?: Date | string
  }

  export type ServiceStatusHistoryUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    recordedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    service?: ServiceStatusUpdateOneRequiredWithoutStatusHistoryNestedInput
  }

  export type ServiceStatusHistoryUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    serviceId?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    recordedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusHistoryCreateManyInput = {
    id?: string
    serviceId: string
    status: string
    recordedAt?: Date | string
    createdAt?: Date | string
  }

  export type ServiceStatusHistoryUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    recordedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusHistoryUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    serviceId?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    recordedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type WorkerProcessCountOrderByAggregateInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    error?: SortOrder
  }

  export type WorkerProcessMaxOrderByAggregateInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    error?: SortOrder
  }

  export type WorkerProcessMinOrderByAggregateInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    error?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type SearchJobCountOrderByAggregateInput = {
    id?: SortOrder
    query?: SortOrder
    filters?: SortOrder
    status?: SortOrder
    results?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    error?: SortOrder
  }

  export type SearchJobMaxOrderByAggregateInput = {
    id?: SortOrder
    query?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    error?: SortOrder
  }

  export type SearchJobMinOrderByAggregateInput = {
    id?: SortOrder
    query?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    error?: SortOrder
  }

  export type FloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type AtsAnalysisCountOrderByAggregateInput = {
    id?: SortOrder
    resumeId?: SortOrder
    overallScore?: SortOrder
    keywordScore?: SortOrder
    formatScore?: SortOrder
    contentScore?: SortOrder
    readabilityScore?: SortOrder
    detectedIssues?: SortOrder
    suggestedKeywords?: SortOrder
    analysisDetails?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AtsAnalysisAvgOrderByAggregateInput = {
    overallScore?: SortOrder
    keywordScore?: SortOrder
    formatScore?: SortOrder
    contentScore?: SortOrder
    readabilityScore?: SortOrder
  }

  export type AtsAnalysisMaxOrderByAggregateInput = {
    id?: SortOrder
    resumeId?: SortOrder
    overallScore?: SortOrder
    keywordScore?: SortOrder
    formatScore?: SortOrder
    contentScore?: SortOrder
    readabilityScore?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AtsAnalysisMinOrderByAggregateInput = {
    id?: SortOrder
    resumeId?: SortOrder
    overallScore?: SortOrder
    keywordScore?: SortOrder
    formatScore?: SortOrder
    contentScore?: SortOrder
    readabilityScore?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AtsAnalysisSumOrderByAggregateInput = {
    overallScore?: SortOrder
    keywordScore?: SortOrder
    formatScore?: SortOrder
    contentScore?: SortOrder
    readabilityScore?: SortOrder
  }

  export type FloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type FloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type ParsedResumeCountOrderByAggregateInput = {
    id?: SortOrder
    resumeId?: SortOrder
    userId?: SortOrder
    profileId?: SortOrder
    parsedAt?: SortOrder
    parserVersion?: SortOrder
    parserType?: SortOrder
    fileType?: SortOrder
    parseTime?: SortOrder
    status?: SortOrder
    name?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    location?: SortOrder
    summary?: SortOrder
    website?: SortOrder
    education?: SortOrder
    experience?: SortOrder
    skills?: SortOrder
    projects?: SortOrder
    certifications?: SortOrder
    languages?: SortOrder
    publications?: SortOrder
    achievements?: SortOrder
    volunteer?: SortOrder
    interests?: SortOrder
    references?: SortOrder
    patents?: SortOrder
    rawText?: SortOrder
    sectionMap?: SortOrder
    confidenceScores?: SortOrder
    overallScore?: SortOrder
  }

  export type ParsedResumeAvgOrderByAggregateInput = {
    parseTime?: SortOrder
    overallScore?: SortOrder
  }

  export type ParsedResumeMaxOrderByAggregateInput = {
    id?: SortOrder
    resumeId?: SortOrder
    userId?: SortOrder
    profileId?: SortOrder
    parsedAt?: SortOrder
    parserVersion?: SortOrder
    parserType?: SortOrder
    fileType?: SortOrder
    parseTime?: SortOrder
    status?: SortOrder
    name?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    location?: SortOrder
    summary?: SortOrder
    website?: SortOrder
    rawText?: SortOrder
    overallScore?: SortOrder
  }

  export type ParsedResumeMinOrderByAggregateInput = {
    id?: SortOrder
    resumeId?: SortOrder
    userId?: SortOrder
    profileId?: SortOrder
    parsedAt?: SortOrder
    parserVersion?: SortOrder
    parserType?: SortOrder
    fileType?: SortOrder
    parseTime?: SortOrder
    status?: SortOrder
    name?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    location?: SortOrder
    summary?: SortOrder
    website?: SortOrder
    rawText?: SortOrder
    overallScore?: SortOrder
  }

  export type ParsedResumeSumOrderByAggregateInput = {
    parseTime?: SortOrder
    overallScore?: SortOrder
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type ServiceStatusHistoryListRelationFilter = {
    every?: ServiceStatusHistoryWhereInput
    some?: ServiceStatusHistoryWhereInput
    none?: ServiceStatusHistoryWhereInput
  }

  export type ServiceStatusHistoryOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ServiceStatusCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    description?: SortOrder
    lastCheckedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ServiceStatusMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    description?: SortOrder
    lastCheckedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ServiceStatusMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    description?: SortOrder
    lastCheckedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ServiceStatusScalarRelationFilter = {
    is?: ServiceStatusWhereInput
    isNot?: ServiceStatusWhereInput
  }

  export type ServiceStatusHistoryCountOrderByAggregateInput = {
    id?: SortOrder
    serviceId?: SortOrder
    status?: SortOrder
    recordedAt?: SortOrder
    createdAt?: SortOrder
  }

  export type ServiceStatusHistoryMaxOrderByAggregateInput = {
    id?: SortOrder
    serviceId?: SortOrder
    status?: SortOrder
    recordedAt?: SortOrder
    createdAt?: SortOrder
  }

  export type ServiceStatusHistoryMinOrderByAggregateInput = {
    id?: SortOrder
    serviceId?: SortOrder
    status?: SortOrder
    recordedAt?: SortOrder
    createdAt?: SortOrder
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type FloatFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableFloatFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type ServiceStatusHistoryCreateNestedManyWithoutServiceInput = {
    create?: XOR<ServiceStatusHistoryCreateWithoutServiceInput, ServiceStatusHistoryUncheckedCreateWithoutServiceInput> | ServiceStatusHistoryCreateWithoutServiceInput[] | ServiceStatusHistoryUncheckedCreateWithoutServiceInput[]
    connectOrCreate?: ServiceStatusHistoryCreateOrConnectWithoutServiceInput | ServiceStatusHistoryCreateOrConnectWithoutServiceInput[]
    createMany?: ServiceStatusHistoryCreateManyServiceInputEnvelope
    connect?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
  }

  export type ServiceStatusHistoryUncheckedCreateNestedManyWithoutServiceInput = {
    create?: XOR<ServiceStatusHistoryCreateWithoutServiceInput, ServiceStatusHistoryUncheckedCreateWithoutServiceInput> | ServiceStatusHistoryCreateWithoutServiceInput[] | ServiceStatusHistoryUncheckedCreateWithoutServiceInput[]
    connectOrCreate?: ServiceStatusHistoryCreateOrConnectWithoutServiceInput | ServiceStatusHistoryCreateOrConnectWithoutServiceInput[]
    createMany?: ServiceStatusHistoryCreateManyServiceInputEnvelope
    connect?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
  }

  export type ServiceStatusHistoryUpdateManyWithoutServiceNestedInput = {
    create?: XOR<ServiceStatusHistoryCreateWithoutServiceInput, ServiceStatusHistoryUncheckedCreateWithoutServiceInput> | ServiceStatusHistoryCreateWithoutServiceInput[] | ServiceStatusHistoryUncheckedCreateWithoutServiceInput[]
    connectOrCreate?: ServiceStatusHistoryCreateOrConnectWithoutServiceInput | ServiceStatusHistoryCreateOrConnectWithoutServiceInput[]
    upsert?: ServiceStatusHistoryUpsertWithWhereUniqueWithoutServiceInput | ServiceStatusHistoryUpsertWithWhereUniqueWithoutServiceInput[]
    createMany?: ServiceStatusHistoryCreateManyServiceInputEnvelope
    set?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    disconnect?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    delete?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    connect?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    update?: ServiceStatusHistoryUpdateWithWhereUniqueWithoutServiceInput | ServiceStatusHistoryUpdateWithWhereUniqueWithoutServiceInput[]
    updateMany?: ServiceStatusHistoryUpdateManyWithWhereWithoutServiceInput | ServiceStatusHistoryUpdateManyWithWhereWithoutServiceInput[]
    deleteMany?: ServiceStatusHistoryScalarWhereInput | ServiceStatusHistoryScalarWhereInput[]
  }

  export type ServiceStatusHistoryUncheckedUpdateManyWithoutServiceNestedInput = {
    create?: XOR<ServiceStatusHistoryCreateWithoutServiceInput, ServiceStatusHistoryUncheckedCreateWithoutServiceInput> | ServiceStatusHistoryCreateWithoutServiceInput[] | ServiceStatusHistoryUncheckedCreateWithoutServiceInput[]
    connectOrCreate?: ServiceStatusHistoryCreateOrConnectWithoutServiceInput | ServiceStatusHistoryCreateOrConnectWithoutServiceInput[]
    upsert?: ServiceStatusHistoryUpsertWithWhereUniqueWithoutServiceInput | ServiceStatusHistoryUpsertWithWhereUniqueWithoutServiceInput[]
    createMany?: ServiceStatusHistoryCreateManyServiceInputEnvelope
    set?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    disconnect?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    delete?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    connect?: ServiceStatusHistoryWhereUniqueInput | ServiceStatusHistoryWhereUniqueInput[]
    update?: ServiceStatusHistoryUpdateWithWhereUniqueWithoutServiceInput | ServiceStatusHistoryUpdateWithWhereUniqueWithoutServiceInput[]
    updateMany?: ServiceStatusHistoryUpdateManyWithWhereWithoutServiceInput | ServiceStatusHistoryUpdateManyWithWhereWithoutServiceInput[]
    deleteMany?: ServiceStatusHistoryScalarWhereInput | ServiceStatusHistoryScalarWhereInput[]
  }

  export type ServiceStatusCreateNestedOneWithoutStatusHistoryInput = {
    create?: XOR<ServiceStatusCreateWithoutStatusHistoryInput, ServiceStatusUncheckedCreateWithoutStatusHistoryInput>
    connectOrCreate?: ServiceStatusCreateOrConnectWithoutStatusHistoryInput
    connect?: ServiceStatusWhereUniqueInput
  }

  export type ServiceStatusUpdateOneRequiredWithoutStatusHistoryNestedInput = {
    create?: XOR<ServiceStatusCreateWithoutStatusHistoryInput, ServiceStatusUncheckedCreateWithoutStatusHistoryInput>
    connectOrCreate?: ServiceStatusCreateOrConnectWithoutStatusHistoryInput
    upsert?: ServiceStatusUpsertWithoutStatusHistoryInput
    connect?: ServiceStatusWhereUniqueInput
    update?: XOR<XOR<ServiceStatusUpdateToOneWithWhereWithoutStatusHistoryInput, ServiceStatusUpdateWithoutStatusHistoryInput>, ServiceStatusUncheckedUpdateWithoutStatusHistoryInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedFloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }
  export type NestedJsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type ServiceStatusHistoryCreateWithoutServiceInput = {
    id?: string
    status: string
    recordedAt?: Date | string
    createdAt?: Date | string
  }

  export type ServiceStatusHistoryUncheckedCreateWithoutServiceInput = {
    id?: string
    status: string
    recordedAt?: Date | string
    createdAt?: Date | string
  }

  export type ServiceStatusHistoryCreateOrConnectWithoutServiceInput = {
    where: ServiceStatusHistoryWhereUniqueInput
    create: XOR<ServiceStatusHistoryCreateWithoutServiceInput, ServiceStatusHistoryUncheckedCreateWithoutServiceInput>
  }

  export type ServiceStatusHistoryCreateManyServiceInputEnvelope = {
    data: ServiceStatusHistoryCreateManyServiceInput | ServiceStatusHistoryCreateManyServiceInput[]
    skipDuplicates?: boolean
  }

  export type ServiceStatusHistoryUpsertWithWhereUniqueWithoutServiceInput = {
    where: ServiceStatusHistoryWhereUniqueInput
    update: XOR<ServiceStatusHistoryUpdateWithoutServiceInput, ServiceStatusHistoryUncheckedUpdateWithoutServiceInput>
    create: XOR<ServiceStatusHistoryCreateWithoutServiceInput, ServiceStatusHistoryUncheckedCreateWithoutServiceInput>
  }

  export type ServiceStatusHistoryUpdateWithWhereUniqueWithoutServiceInput = {
    where: ServiceStatusHistoryWhereUniqueInput
    data: XOR<ServiceStatusHistoryUpdateWithoutServiceInput, ServiceStatusHistoryUncheckedUpdateWithoutServiceInput>
  }

  export type ServiceStatusHistoryUpdateManyWithWhereWithoutServiceInput = {
    where: ServiceStatusHistoryScalarWhereInput
    data: XOR<ServiceStatusHistoryUpdateManyMutationInput, ServiceStatusHistoryUncheckedUpdateManyWithoutServiceInput>
  }

  export type ServiceStatusHistoryScalarWhereInput = {
    AND?: ServiceStatusHistoryScalarWhereInput | ServiceStatusHistoryScalarWhereInput[]
    OR?: ServiceStatusHistoryScalarWhereInput[]
    NOT?: ServiceStatusHistoryScalarWhereInput | ServiceStatusHistoryScalarWhereInput[]
    id?: StringFilter<"ServiceStatusHistory"> | string
    serviceId?: StringFilter<"ServiceStatusHistory"> | string
    status?: StringFilter<"ServiceStatusHistory"> | string
    recordedAt?: DateTimeFilter<"ServiceStatusHistory"> | Date | string
    createdAt?: DateTimeFilter<"ServiceStatusHistory"> | Date | string
  }

  export type ServiceStatusCreateWithoutStatusHistoryInput = {
    id?: string
    name: string
    status: string
    description?: string | null
    lastCheckedAt?: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ServiceStatusUncheckedCreateWithoutStatusHistoryInput = {
    id?: string
    name: string
    status: string
    description?: string | null
    lastCheckedAt?: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ServiceStatusCreateOrConnectWithoutStatusHistoryInput = {
    where: ServiceStatusWhereUniqueInput
    create: XOR<ServiceStatusCreateWithoutStatusHistoryInput, ServiceStatusUncheckedCreateWithoutStatusHistoryInput>
  }

  export type ServiceStatusUpsertWithoutStatusHistoryInput = {
    update: XOR<ServiceStatusUpdateWithoutStatusHistoryInput, ServiceStatusUncheckedUpdateWithoutStatusHistoryInput>
    create: XOR<ServiceStatusCreateWithoutStatusHistoryInput, ServiceStatusUncheckedCreateWithoutStatusHistoryInput>
    where?: ServiceStatusWhereInput
  }

  export type ServiceStatusUpdateToOneWithWhereWithoutStatusHistoryInput = {
    where?: ServiceStatusWhereInput
    data: XOR<ServiceStatusUpdateWithoutStatusHistoryInput, ServiceStatusUncheckedUpdateWithoutStatusHistoryInput>
  }

  export type ServiceStatusUpdateWithoutStatusHistoryInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    lastCheckedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusUncheckedUpdateWithoutStatusHistoryInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    lastCheckedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusHistoryCreateManyServiceInput = {
    id?: string
    status: string
    recordedAt?: Date | string
    createdAt?: Date | string
  }

  export type ServiceStatusHistoryUpdateWithoutServiceInput = {
    id?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    recordedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusHistoryUncheckedUpdateWithoutServiceInput = {
    id?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    recordedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ServiceStatusHistoryUncheckedUpdateManyWithoutServiceInput = {
    id?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    recordedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}