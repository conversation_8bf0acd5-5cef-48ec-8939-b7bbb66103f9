generator client {
  provider        = "prisma-client-js"
  output          = "../node_modules/.prisma/client"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["workers", "web"]
}

model WorkerProcess {
  id          String    @id @default(cuid())
  type        String
  status      String
  data        Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  startedAt   DateTime?
  completedAt DateTime?
  error       String?

  @@map("WorkerProcess")
  @@schema("workers")
}

model SearchJob {
  id          String    @id @default(cuid())
  query       String
  filters     Json?
  status      String
  results     Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  startedAt   DateTime?
  completedAt DateTime?
  error       String?

  @@map("SearchJob")
  @@schema("workers")
}

model AtsAnalysis {
  id                String   @id @default(cuid())
  resumeId          String   @unique
  overallScore      Float
  keywordScore      Float
  formatScore       Float
  contentScore      Float
  readabilityScore  Float
  detectedIssues    Json
  suggestedKeywords Json
  analysisDetails   Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("AtsAnalysis")
  @@schema("workers")
}

model ParsedResume {
  id               String   @id
  resumeId         String   @unique
  userId           String
  profileId        String?
  parsedAt         DateTime @default(now())
  parserVersion    String
  parserType       String   @default("enhanced")
  fileType         String?
  parseTime        Int?
  status           String   @default("success")
  name             String?
  email            String?
  phone            String?
  location         String?
  summary          String?
  website          String?
  education        Json?
  experience       Json?
  skills           Json?
  projects         Json?
  certifications   Json?
  languages        Json?
  publications     Json?
  achievements     Json?
  volunteer        Json?
  interests        Json?
  references       Json?
  patents          Json?
  rawText          String?
  sectionMap       Json?
  confidenceScores Json?
  overallScore     Float?

  @@map("ParsedResume")
  @@schema("workers")
}

model ServiceStatus {
  id            String                 @id @default(uuid())
  name          String                 @unique
  status        String
  description   String?
  lastCheckedAt DateTime               @default(now())
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @updatedAt
  statusHistory ServiceStatusHistory[]

  @@schema("web")
}

model ServiceStatusHistory {
  id         String        @id @default(uuid())
  serviceId  String
  status     String
  recordedAt DateTime      @default(now())
  createdAt  DateTime      @default(now())
  service    ServiceStatus @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@schema("web")
}
