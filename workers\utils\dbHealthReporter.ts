// workers/utils/dbHealthReporter.ts
// Database-based health reporter that saves to ServiceStatus tables

import { prisma } from "./prisma.js";
import { Logger } from "./logger.js";

export interface HealthMetrics {
  cpuUsage?: number;
  memoryUsage?: number;
  activeWorkers?: number;
  queueSize?: number;
  errorRate?: number;
  uptime?: number;
  responseTime?: number;
  [key: string]: any;
}

export type HealthStatus =
  | "operational"
  | "degraded"
  | "outage"
  | "maintenance";

export interface HealthReport {
  service: string;
  status: HealthStatus;
  details?: HealthMetrics;
  timestamp?: string;
}

export class DatabaseHealthReporter {
  private readonly serviceName: string;
  private readonly reportInterval: number;
  private intervalId: NodeJS.Timeout | null = null;
  private lastReportTime = 0;
  private readonly REPORT_INTERVAL_HOURS = 1; // Report every hour
  private readonly HISTORY_RETENTION_DAYS = 30; // Keep 30 days of history

  constructor(
    serviceName: string,
    reportInterval: number = 3600000 // 1 hour default
  ) {
    this.serviceName = serviceName;
    this.reportInterval = reportInterval;
  }

  /**
   * Report current health status to the database
   */
  async reportHealth(
    status: HealthStatus,
    metrics?: HealthMetrics
  ): Promise<boolean> {
    try {
      // Use the imported prisma client

      // Find or create service status record
      let serviceStatus = await prisma.serviceStatus.findUnique({
        where: { name: this.serviceName },
      });

      const now = new Date();
      const statusChanged = serviceStatus?.status !== status;

      if (!serviceStatus) {
        // Create new service status record
        serviceStatus = await prisma.serviceStatus.create({
          data: {
            name: this.serviceName,
            status,
            description: this.getServiceDescription(this.serviceName),
            lastCheckedAt: now,
          },
        });

        Logger.info(
          `Created new service status record for ${this.serviceName}`
        );
      } else {
        // Update existing service status
        await prisma.serviceStatus.update({
          where: { id: serviceStatus.id },
          data: {
            status,
            lastCheckedAt: now,
          },
        });
      }

      // Record status change in history (only if status changed or it's been an hour)
      const shouldRecordHistory =
        statusChanged || this.shouldRecordHourlyHistory();

      if (shouldRecordHistory) {
        await prisma.serviceStatusHistory.create({
          data: {
            serviceId: serviceStatus.id,
            status,
            recordedAt: now,
          },
        });

        if (statusChanged) {
          Logger.info(
            `Status changed for ${this.serviceName}: ${serviceStatus.status} -> ${status}`
          );
        } else {
          Logger.debug(
            `Hourly health report recorded for ${this.serviceName}: ${status}`
          );
        }

        this.lastReportTime = Date.now();
      }

      // Clean up old history records (keep only last 30 days)
      await this.cleanupOldHistory(serviceStatus.id);

      return true;
    } catch (error) {
      Logger.error(`Error reporting health for ${this.serviceName}:`, error);
      return false;
    }
  }

  /**
   * Check if we should record hourly history
   */
  private shouldRecordHourlyHistory(): boolean {
    const now = Date.now();
    const hourInMs = 60 * 60 * 1000; // 1 hour in milliseconds
    return now - this.lastReportTime >= hourInMs;
  }

  /**
   * Clean up old history records (keep only last 30 days)
   */
  private async cleanupOldHistory(serviceId: string): Promise<void> {
    try {
      // Use the imported prisma client
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.HISTORY_RETENTION_DAYS);

      const deletedCount = await prisma.serviceStatusHistory.deleteMany({
        where: {
          serviceId,
          recordedAt: {
            lt: cutoffDate,
          },
        },
      });

      if (deletedCount.count > 0) {
        Logger.debug(
          `Cleaned up ${deletedCount.count} old history records for ${this.serviceName}`
        );
      }
    } catch (error) {
      Logger.warn(
        `Error cleaning up old history for ${this.serviceName}:`,
        error
      );
    }
  }

  /**
   * Get service description based on service name
   */
  private getServiceDescription(serviceName: string): string {
    const descriptions: Record<string, string> = {
      "auto-apply-worker": "Background job processing and automation",
      enrichJobDetails: "Job listing enrichment and data processing",
      parallelJobScraper: "Job scraping and data collection",
      scrapeJobDetails: "Detailed job information scraping",
      updateJobMatches: "Job matching and recommendation updates",
      marketAnalyticsCollector: "Market analytics and trend analysis",
      domainUpdate: "Company domain information updates",
      checkExperienceRequirements: "Experience requirement analysis",
      dailySummary: "Daily job processing summaries",
      "auto-apply-ai": "AI services for resume optimization and analysis",
      "auto-apply-scraper": "Web scraping services for job data",
    };

    return descriptions[serviceName] || `${serviceName} service`;
  }

  /**
   * Start automatic health reporting
   */
  startReporting(
    getHealthStatus: () => Promise<{
      status: HealthStatus;
      metrics?: HealthMetrics;
    }>
  ): void {
    if (this.intervalId) {
      Logger.warn(`Health reporting already started for ${this.serviceName}`);
      return;
    }

    Logger.info(
      `🏥 Starting database health reporting for ${this.serviceName} (interval: ${this.reportInterval / 1000 / 60} minutes)`
    );

    this.intervalId = setInterval(async () => {
      try {
        const { status, metrics } = await getHealthStatus();
        await this.reportHealth(status, metrics);
      } catch (error) {
        Logger.error(
          `Error getting health status for ${this.serviceName}:`,
          error
        );
        await this.reportHealth("outage", { error: "Health check failed" });
      }
    }, this.reportInterval);

    // Report initial status immediately
    getHealthStatus()
      .then(({ status, metrics }) => this.reportHealth(status, metrics))
      .catch((error) => {
        Logger.error(
          `Error getting initial health status for ${this.serviceName}:`,
          error
        );
        this.reportHealth("outage", { error: "Initial health check failed" });
      });
  }

  /**
   * Stop automatic health reporting
   */
  stopReporting(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      Logger.info(`🛑 Stopped health reporting for ${this.serviceName}`);
    }
  }

  /**
   * Report service shutdown
   */
  async reportShutdown(): Promise<void> {
    Logger.info(`📴 Reporting shutdown for ${this.serviceName}`);
    await this.reportHealth("maintenance", {
      message: "Service shutting down",
      shutdownTime: new Date().toISOString(),
    });
  }

  /**
   * Determine health status based on common metrics
   */
  static determineHealthStatus(metrics: HealthMetrics): HealthStatus {
    const {
      cpuUsage = 0,
      memoryUsage = 0,
      errorRate = 0,
      responseTime = 0,
    } = metrics;

    // Critical thresholds
    if (
      cpuUsage > 95 ||
      memoryUsage > 95 ||
      errorRate > 50 ||
      responseTime > 30000
    ) {
      return "outage";
    }

    // Degraded thresholds
    if (
      cpuUsage > 80 ||
      memoryUsage > 80 ||
      errorRate > 10 ||
      responseTime > 10000
    ) {
      return "degraded";
    }

    return "operational";
  }

  /**
   * Get service uptime in seconds
   */
  static getUptime(): number {
    return process.uptime();
  }

  /**
   * Get memory usage metrics
   */
  static getMemoryMetrics(): {
    memoryUsage: number;
    memoryUsed: number;
    memoryTotal: number;
  } {
    const memUsage = process.memoryUsage();
    const totalMemory = memUsage.heapTotal + memUsage.external;
    const usedMemory = memUsage.heapUsed;

    return {
      memoryUsage: totalMemory > 0 ? (usedMemory / totalMemory) * 100 : 0,
      memoryUsed: usedMemory,
      memoryTotal: totalMemory,
    };
  }
}
